# Generated by Django 5.2 on 2025-04-14 07:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('candidates', '0004_alter_candidate_optional_roles_and_more'),
        ('jobs', '0004_alter_job_title'),
    ]

    operations = [
        migrations.CreateModel(
            name='CandidateRanking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(default=0)),
                ('reasoning', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rankings', to='candidates.candidate')),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_rankings', to='jobs.job')),
            ],
            options={
                'ordering': ['-score', '-updated_at'],
                'unique_together': {('candidate', 'job')},
            },
        ),
    ]
