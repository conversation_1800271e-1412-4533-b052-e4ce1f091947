<!DOCTYPE html>
<html>
<head>
    <title>Upload Interview Transcript</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, select, button {
            padding: 8px;
            width: 100%;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <h1>Upload Interview Transcript</h1>
    
    <form method="post" enctype="multipart/form-data" action="/api/candidates/upload-transcript-direct/">
        {% csrf_token %}
        <div class="form-group">
            <label for="candidate">Candidate ID:</label>
            <input type="number" id="candidate" name="candidate" required>
        </div>
        
        <div class="form-group">
            <label for="level">Interview Level:</label>
            <select id="level" name="level" required>
                <option value="L1">Level 1</option>
                <option value="L2">Level 2</option>
                <option value="L3">Level 3</option>
                <option value="L4">Level 4</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="transcript_file">PDF Transcript:</label>
            <input type="file" id="transcript_file" name="transcript_file" accept="application/pdf" required>
        </div>
        
        <button type="submit">Upload Transcript</button>
    </form>
    
    {% if message %}
    <div class="result {% if success %}success{% else %}error{% endif %}">
        {{ message }}
    </div>
    {% endif %}
</body>
</html>
