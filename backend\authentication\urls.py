from django.urls import path
from .views import register_user, CustomAuthToken, AdminUserCreationView, UserListView, LogoutView, bulk_deactivate_users, bulk_delete_users

urlpatterns = [
    path('register/', register_user, name='register'),
    path('login/', CustomAuthToken.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('admin/create-user/', AdminUserCreationView.as_view(), name='admin-create-user'),
    path('users/', UserListView.as_view(), name='user-list'),
    path('users/bulk-deactivate/', bulk_deactivate_users, name='bulk-deactivate-users'),
    path('users/bulk-delete/', bulk_delete_users, name='bulk-delete-users'),
]
