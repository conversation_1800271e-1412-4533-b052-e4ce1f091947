from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    CandidateViewSet, rank_candidates, InterviewTranscriptViewSet,
    TranscriptEvaluationViewSet, evaluate_transcript, download_evaluation_report,
    upload_transcript_direct, simple_test_view, debug_transcripts
)

# Create a single router for all resources
router = DefaultRouter()
router.register(r'transcripts', InterviewTranscriptViewSet, basename='transcript')
router.register(r'evaluations', TranscriptEvaluationViewSet, basename='evaluation')
router.register(r'', CandidateViewSet, basename='candidate')

# Define URL patterns
urlpatterns = [
    # Custom action endpoints
    path('rank-candidates/', rank_candidates, name='rank-candidates'),
    path('evaluate-transcript/', evaluate_transcript, name='evaluate-transcript'),
    path('download-evaluation-report/', download_evaluation_report, name='download-evaluation-report'),
    path('upload-transcript-direct/', upload_transcript_direct, name='upload-transcript-direct'),
    path('test/', simple_test_view, name='simple-test-view'),
    path('debug-transcripts/', debug_transcripts, name='debug-transcripts'),
    
    # Make sure we can catch the custom upload action - place this BEFORE the router includes
    path('transcripts/upload/', InterviewTranscriptViewSet.as_view({'post': 'upload'}), name='transcript-upload'),
    
    # Include all router URLs
    path('', include(router.urls)),
]
