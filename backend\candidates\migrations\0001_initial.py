# Generated by Django 5.2 on 2025-04-13 19:54

import candidates.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Candidate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('preferred_role', models.CharField(blank=True, choices=[('devops', 'DevOps Engineer'), ('data_analyst', 'Data Analyst'), ('qa_testing', 'QA Testing Engineer'), ('java_fullstack', 'Java Full Stack Engineer'), ('python_developer', 'Python Developer'), ('servicevow', 'ServiceVow Specialist'), ('rpa_developer', 'RPA Developer')], max_length=50, null=True)),
                ('optional_roles', models.CharField(blank=True, max_length=255, null=True)),
                ('resume', models.FileField(blank=True, null=True, upload_to=candidates.models.resume_upload_path)),
                ('total_experience', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True)),
                ('last_job_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_candidates', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
