import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

/**
 * AdminRoute - Special protected route that only allows admin users
 * Redirects to dashboard if the user is not an admin
 */
const AdminRoute = () => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  // Check if user is logged in AND is an admin
  return (currentUser && currentUser.is_admin) 
    ? <Outlet /> 
    : <Navigate to="/dashboard" replace />;
};

export default AdminRoute;
