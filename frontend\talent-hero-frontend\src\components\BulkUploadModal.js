import React, { useState } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import { useAuth } from '../contexts/AuthContext';

const BulkUploadModal = ({ isOpen, onClose, onCandidatesAdded, customJobTitles, onCustomOptionsUpdate, darkMode = false }) => {
  const [csvFile, setCsvFile] = useState(null);
  const [csvData, setCsvData] = useState([]);
  const [previewData, setPreviewData] = useState([]);
  const [unmappedSkills, setUnmappedSkills] = useState([]);
  const [skillMappings, setSkillMappings] = useState({});
  const [availableRoles, setAvailableRoles] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1); // 1: Upload, 2: Preview, 3: Skill Mapping, 4: Confirm
  const { token } = useAuth();

  const expectedHeaders = [
    'PrimarySkill', 'CandidateID', 'CandidateName', 'PrimaryEmail', 
    'Mobile', 'ClientSPOC', 'Schd1PanelName', 'Schd1Date', 'Schd1Time', 'FinalStatus'
  ];

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'text/csv') {
      setCsvFile(file);
      setError('');
    } else {
      setError('Please select a valid CSV file');
      setCsvFile(null);
    }
  };

  const parseCSV = (text) => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length === 0) return { headers: [], rows: [] };

    const headers = lines[0].split(',').map(h => h.trim());
    const rows = [];

    console.log('CSV Headers found:', headers);
    console.log('Expected headers:', expectedHeaders);

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      console.log(`Row ${i} values:`, values);

      if (values.length === headers.length && values.some(v => v !== '')) {
        const row = {};
        headers.forEach((header, index) => {
          row[header] = values[index] || '';
        });
        rows.push(row);
        console.log(`Parsed row ${i}:`, row);
      } else {
        console.warn(`Skipping row ${i} - length mismatch or empty row`);
      }
    }

    console.log('Total rows parsed:', rows.length);
    return { headers, rows };
  };

  const mapCsvToCandidate = (csvRow, useFinalMapping = true) => {
    console.log('Mapping CSV row:', csvRow);

    const mappedRole = mapPrimarySkill(csvRow.PrimarySkill || '', useFinalMapping);

    // Map CSV fields to candidate fields
    const candidate = {
      name: csvRow.CandidateName || '',
      candidate_id: csvRow.CandidateID || '',
      preferred_role: mappedRole || 'UNMAPPED',
      original_skill: csvRow.PrimarySkill || '', // Keep original for reference
      primary_email: csvRow.PrimaryEmail || '',
      mobile: csvRow.Mobile || '',
      spoc: csvRow.ClientSPOC || '',
      hiring_status: 'no_engagement',
      // L1 Interview details
      l1_panel_name: csvRow.Schd1PanelName || '',
      l1_schedule_date: formatDate(csvRow.Schd1Date || ''),
      l1_schedule_time: formatTime(csvRow.Schd1Time || ''),
      l1_status: mapFinalStatus(csvRow.FinalStatus || ''),
      // Interview levels for new system
      interview_levels: [{
        level_name: 'L1',
        level_order: 1,
        status: mapFinalStatus(csvRow.FinalStatus || ''),
        schedule_date: formatDate(csvRow.Schd1Date || ''),
        schedule_time: formatTime(csvRow.Schd1Time || ''),
        panel_name: csvRow.Schd1PanelName || '',
        panel_comment: ''
      }]
    };

    console.log('Mapped candidate:', candidate);
    return candidate;
  };

  const getKnownSkillMapping = (skill) => {
    const skillMap = {
      'devops': 'devops',
      'data analyst': 'data_analyst',
      'qa testing': 'qa_testing',
      'java fullstack': 'java_fullstack',
      'java full stack': 'java_fullstack',
      'python developer': 'python_developer',
      'python': 'python_developer',
      'servicenow': 'servicenow',
      'rpa developer': 'rpa_developer',
      'rpa': 'rpa_developer'
    };

    const normalizedSkill = skill.toLowerCase().trim();
    return skillMap[normalizedSkill] || null;
  };

  const mapPrimarySkill = (skill, useCustomMapping = false) => {
    // First try known mappings
    const knownMapping = getKnownSkillMapping(skill);
    if (knownMapping) {
      return knownMapping;
    }

    // If using custom mapping (after user selection)
    if (useCustomMapping && skillMappings[skill]) {
      return skillMappings[skill];
    }

    // Return null for unmapped skills (we'll handle this in the UI)
    return null;
  };

  const mapFinalStatus = (status) => {
    const statusMap = {
      'scheduled': 'scheduled',
      'attended': 'attended',
      'hold': 'hold',
      'rejected': 'rejected',
      'reject': 'rejected',
      'select': 'selected',
      'selected': 'selected',
      'dropoff': 'dropoff',
      'drop off': 'dropoff'
    };

    const normalizedStatus = status.toLowerCase().trim();
    console.log('Mapping status:', status, '->', statusMap[normalizedStatus] || '');
    return statusMap[normalizedStatus] || '';
  };

  const formatDate = (dateStr) => {
    if (!dateStr || dateStr.trim() === '') return '';

    console.log('Formatting date:', dateStr);

    // Handle DD-MMM-YY format (e.g., "09-Apr-25")
    const ddMmmYyPattern = /^(\d{1,2})-([A-Za-z]{3})-(\d{2})$/;
    const ddMmmYyMatch = dateStr.match(ddMmmYyPattern);

    if (ddMmmYyMatch) {
      const [, day, monthAbbr, year] = ddMmmYyMatch;

      // Map month abbreviations to numbers
      const monthMap = {
        'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
        'may': '05', 'jun': '06', 'jul': '07', 'aug': '08',
        'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
      };

      const monthNum = monthMap[monthAbbr.toLowerCase()];
      if (monthNum) {
        // Convert 2-digit year to 4-digit (assuming 20xx for years 00-99)
        const fullYear = `20${year}`;
        const formatted = `${fullYear}-${monthNum}-${day.padStart(2, '0')}`;
        console.log('Formatted date result (DD-MMM-YY):', formatted);
        return formatted;
      }
    }

    // Try to parse other date formats
    let date = new Date(dateStr);

    if (isNaN(date.getTime())) {
      // Try DD/MM/YYYY format
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        date = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
      }

      // Try DD-MM-YYYY format
      if (isNaN(date.getTime())) {
        const parts = dateStr.split('-');
        if (parts.length === 3 && parts[0].length <= 2) {
          const [day, month, year] = parts;
          date = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        }
      }

      if (isNaN(date.getTime())) {
        console.warn('Could not parse date:', dateStr);
        return '';
      }
    }

    const formatted = date.toISOString().split('T')[0];
    console.log('Formatted date result:', formatted);
    return formatted;
  };

  const formatTime = (timeStr) => {
    if (!timeStr || timeStr.trim() === '') return '';

    console.log('Formatting time:', timeStr);

    // Clean up the time string - remove ? characters and extra spaces
    let time = timeStr.trim().replace(/\?/g, '');

    // Already in 24-hour format
    if (time.match(/^\d{1,2}:\d{2}$/)) {
      const [hours, minutes] = time.split(':');
      const formatted = `${hours.padStart(2, '0')}:${minutes}`;
      console.log('Time formatted (24h):', formatted);
      return formatted;
    }

    // Handle 12-hour format with or without space before AM/PM
    // Patterns: "4:00PM", "4:00 PM", "11:00AM", "11:00 AM"
    const timePattern = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
    const match = time.match(timePattern);

    if (match) {
      const [, hours, minutes, period] = match;
      let hour24 = parseInt(hours);

      if (period.toUpperCase() === 'PM' && hour24 !== 12) {
        hour24 += 12;
      } else if (period.toUpperCase() === 'AM' && hour24 === 12) {
        hour24 = 0;
      }

      const formatted = `${hour24.toString().padStart(2, '0')}:${minutes}`;
      console.log('Time formatted (12h to 24h):', formatted);
      return formatted;
    }

    console.warn('Could not parse time:', timeStr);
    return '';
  };

  const loadAvailableRoles = async () => {
    try {
      // First try to load from API
      const response = await axios.get(`${API_URL}/api/roles/choices/`, {
        headers: {
          'Authorization': `Token ${token}`
        }
      });

      if (response.data && response.data.length > 0) {
        setAvailableRoles(response.data);
        return;
      }
    } catch (error) {
      console.warn('Failed to load roles from API, falling back to defaults:', error);
    }

    // Fallback to passed customJobTitles or localStorage or defaults
    let roles = [];
    if (customJobTitles && customJobTitles.length > 0) {
      roles = customJobTitles;
    } else {
      const savedRoles = JSON.parse(localStorage.getItem('customJobTitles') || '[]');
      if (savedRoles.length > 0) {
        roles = savedRoles;
      } else {
        roles = [
          { value: 'devops', label: 'DevOps Engineer' },
          { value: 'data_analyst', label: 'Data Analyst' },
          { value: 'qa_testing', label: 'QA Testing Engineer' },
          { value: 'java_fullstack', label: 'Java Full Stack Engineer' },
          { value: 'python_developer', label: 'Python Developer' },
          { value: 'servicenow', label: 'ServiceNow Specialist' },
          { value: 'rpa_developer', label: 'RPA Developer' }
        ];
      }
    }

    setAvailableRoles(roles);
  };

  const handleFileUpload = async () => {
    if (!csvFile) {
      setError('Please select a CSV file');
      return;
    }

    setIsProcessing(true);
    setError('');

    try {
      // Load available roles
      loadAvailableRoles();

      const text = await csvFile.text();
      const { headers, rows } = parseCSV(text);

      // Validate headers
      const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
      if (missingHeaders.length > 0) {
        setError(`Missing required headers: ${missingHeaders.join(', ')}`);
        setIsProcessing(false);
        return;
      }

      // Detect unmapped skills
      const uniqueSkills = [...new Set(rows.map(row => row.PrimarySkill).filter(skill => skill))];
      const unmapped = uniqueSkills.filter(skill => !getKnownSkillMapping(skill));

      console.log('Unique skills found:', uniqueSkills);
      console.log('Unmapped skills:', unmapped);

      // Process rows (without final skill mapping yet)
      const candidates = rows.map(row => mapCsvToCandidate(row, false));
      setCsvData(candidates);
      setPreviewData(candidates); // Show all candidates for preview
      setUnmappedSkills(unmapped);

      // If there are unmapped skills, go to skill mapping step, otherwise go to preview
      setStep(unmapped.length > 0 ? 3 : 2);

    } catch (err) {
      setError('Error processing CSV file: ' + err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirmUpload = async () => {
    setIsUploading(true);
    setError('');

    try {
      const results = [];
      
      for (const candidateData of csvData) {
        try {
          console.log('Processing candidate:', candidateData);

          const formData = new FormData();

          // Add candidate data
          Object.keys(candidateData).forEach(key => {
            if (key === 'interview_levels') {
              formData.append(key, JSON.stringify(candidateData[key]));
              console.log('Added interview_levels:', JSON.stringify(candidateData[key]));
            } else if (candidateData[key] !== null && candidateData[key] !== undefined && candidateData[key] !== '') {
              formData.append(key, candidateData[key]);
              console.log(`Added ${key}:`, candidateData[key]);

              // Also send role values for dynamic role creation
              if (key === 'preferred_role') {
                formData.append('preferred_role_value', candidateData[key]);
                console.log('Added preferred_role_value:', candidateData[key]);
              }
              if (key === 'optional_roles') {
                formData.append('optional_roles_value', candidateData[key]);
                console.log('Added optional_roles_value:', candidateData[key]);
              }
            }
          });

          console.log('Sending request to:', `${API_URL}/api/candidates/`);
          const response = await axios.post(`${API_URL}/api/candidates/`, formData, {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'multipart/form-data'
            }
          });

          console.log('Candidate created successfully:', response.data);
          results.push({ success: true, data: response.data });
        } catch (err) {
          console.error('Error creating candidate:', err);
          console.error('Error response:', err.response?.data);
          console.error('Error status:', err.response?.status);

          results.push({
            success: false,
            error: err.response?.data?.detail || err.response?.data?.error || err.message,
            candidateName: candidateData.name
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        onCandidatesAdded(results.filter(r => r.success).map(r => r.data));
      }

      if (failureCount > 0) {
        const failedCandidates = results.filter(r => !r.success);
        setError(`${successCount} candidates created successfully. ${failureCount} failed: ${failedCandidates.map(f => f.candidateName).join(', ')}`);
      } else {
        onClose();
      }

    } catch (err) {
      setError('Error uploading candidates: ' + err.message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSkillMapping = (originalSkill, mappedRole) => {
    setSkillMappings(prev => ({
      ...prev,
      [originalSkill]: mappedRole
    }));
  };

  const createNewRole = async (skillName) => {
    // Create a new role value from the skill name (following CustomizeMenu pattern)
    const formattedValue = skillName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const newRole = { value: formattedValue, label: skillName };

    // Check for duplicates
    if (availableRoles.some(role => role.value === formattedValue)) {
      console.warn('Role already exists:', formattedValue);
      handleSkillMapping(skillName, formattedValue);
      return;
    }

    try {
      // Create role via API
      const response = await axios.post(`${API_URL}/api/roles/`, newRole, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data) {
        // Add to available roles
        const updatedRoles = [...availableRoles, { value: response.data.value, label: response.data.label }];
        setAvailableRoles(updatedRoles);
        handleSkillMapping(skillName, response.data.value);

        // Also save to localStorage as backup
        localStorage.setItem('customJobTitles', JSON.stringify(updatedRoles));

        // Notify parent component about the update (if callback provided)
        if (onCustomOptionsUpdate) {
          onCustomOptionsUpdate({ jobTitles: updatedRoles });
        }

        console.log('Created new role via API:', response.data);
      }
    } catch (error) {
      console.error('Failed to create role via API:', error);

      // Fallback to localStorage method
      const updatedRoles = [...availableRoles, newRole];
      setAvailableRoles(updatedRoles);
      handleSkillMapping(skillName, formattedValue);
      localStorage.setItem('customJobTitles', JSON.stringify(updatedRoles));

      if (onCustomOptionsUpdate) {
        onCustomOptionsUpdate({ jobTitles: updatedRoles });
      }

      console.log('Created new role locally (API failed):', newRole);
    }
  };

  const proceedToPreview = () => {
    // Re-map all candidates with final skill mappings
    const finalCandidates = csvData.map(candidate => ({
      ...candidate,
      preferred_role: mapPrimarySkill(candidate.original_skill, true) || 'java_fullstack'
    }));

    setCsvData(finalCandidates);
    setPreviewData(finalCandidates);
    setStep(2);
  };

  const resetModal = () => {
    setCsvFile(null);
    setCsvData([]);
    setPreviewData([]);
    setUnmappedSkills([]);
    setSkillMappings({});
    setAvailableRoles([]);
    setStep(1);
    setError('');
    setIsProcessing(false);
    setIsUploading(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto`}>
        <div className={`p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex justify-between items-center">
            <h2 className={`text-xl font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              Bulk Upload Candidates
            </h2>
            <button
              onClick={handleClose}
              className={`${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'}`}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {step === 1 && (
            <div>
              <div className={`mb-4 p-4 ${darkMode ? 'bg-blue-900 border-blue-700' : 'bg-blue-50 border-blue-200'} border rounded-lg`}>
                <p className={`text-sm ${darkMode ? 'text-blue-200' : 'text-blue-800'} mb-2`}>
                  Please upload a CSV with headers in this format (and order):
                </p>
                <p className={`text-xs font-mono ${darkMode ? 'text-blue-300' : 'text-blue-900'} bg-opacity-50 p-2 rounded`}>
                  PrimarySkill, CandidateID, CandidateName, PrimaryEmail, Mobile, ClientSPOC, Schd1PanelName, Schd1Date, Schd1Time, FinalStatus
                </p>
              </div>

              <div className="mb-4">
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                  Select CSV File
                </label>
                <input
                  type="file"
                  accept=".csv"
                  onChange={handleFileChange}
                  className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleClose}
                  className={`px-4 py-2 ${darkMode ? 'bg-gray-600 hover:bg-gray-700 text-white' : 'bg-gray-300 hover:bg-gray-400 text-gray-800'} rounded`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleFileUpload}
                  disabled={!csvFile || isProcessing}
                  className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isProcessing ? 'Processing...' : 'Process CSV'}
                </button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>
                Preview - {csvData.length} candidates will be created
              </h3>

              <div className="mb-4 max-h-96 overflow-y-auto">
                <div className="grid gap-4">
                  {previewData.map((candidate, index) => (
                    <div key={index} className={`p-3 border ${darkMode ? 'border-gray-600 bg-gray-700 text-gray-100' : 'border-gray-200 bg-gray-50 text-gray-800'} rounded`}>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                        <div><strong>Name:</strong> {candidate.name}</div>
                        <div><strong>ID:</strong> {candidate.candidate_id}</div>
                        <div><strong>Email:</strong> {candidate.primary_email}</div>
                        <div><strong>Role:</strong> {candidate.preferred_role}</div>
                        <div><strong>L1 Status:</strong> {candidate.l1_status}</div>
                        <div><strong>L1 Date:</strong> {candidate.l1_schedule_date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setStep(1)}
                  className={`px-4 py-2 ${darkMode ? 'bg-gray-600 hover:bg-gray-700 text-white' : 'bg-gray-300 hover:bg-gray-400 text-gray-800'} rounded`}
                >
                  Back
                </button>
                <button
                  onClick={handleConfirmUpload}
                  disabled={isUploading}
                  className={`px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isUploading ? 'Creating Candidates...' : `Create ${csvData.length} Candidates`}
                </button>
              </div>
            </div>
          )}

          {step === 3 && (
            <div>
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-800'} mb-4`}>
                Map Skills to Roles
              </h3>

              <div className={`mb-4 p-4 ${darkMode ? 'bg-blue-900 border-blue-700' : 'bg-blue-50 border-blue-200'} border rounded-lg`}>
                <p className={`text-sm ${darkMode ? 'text-blue-200' : 'text-blue-800'} mb-2`}>
                  The following skills from your CSV don't match existing roles. Please choose how to handle them:
                </p>
              </div>

              <div className="space-y-4 mb-6">
                {unmappedSkills.map((skill, index) => (
                  <div key={index} className={`p-4 border ${darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'} rounded`}>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                        "{skill}"
                      </h4>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                      <div className="flex-1">
                        <label className={`block text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                          Map to existing role:
                        </label>
                        <select
                          value={skillMappings[skill] || ''}
                          onChange={(e) => handleSkillMapping(skill, e.target.value)}
                          className={`w-full p-2 border ${darkMode ? 'bg-gray-600 border-gray-500 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        >
                          <option value="">Select existing role...</option>
                          {availableRoles.map(role => (
                            <option key={role.value} value={role.value}>
                              {role.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="flex items-end">
                        <button
                          onClick={() => createNewRole(skill)}
                          className={`px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 whitespace-nowrap`}
                        >
                          Create New Role
                        </button>
                      </div>
                    </div>

                    {skillMappings[skill] && (
                      <div className={`mt-2 text-sm ${darkMode ? 'text-green-400' : 'text-green-600'}`}>
                        ✓ Mapped to: {availableRoles.find(r => r.value === skillMappings[skill])?.label || skillMappings[skill]}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setStep(1)}
                  className={`px-4 py-2 ${darkMode ? 'bg-gray-600 hover:bg-gray-700 text-white' : 'bg-gray-300 hover:bg-gray-400 text-gray-800'} rounded`}
                >
                  Back
                </button>
                <button
                  onClick={proceedToPreview}
                  disabled={unmappedSkills.some(skill => !skillMappings[skill])}
                  className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  Continue to Preview
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkUploadModal;
