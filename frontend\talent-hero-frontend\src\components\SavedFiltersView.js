import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import RankCandidatesModal from './RankCandidatesModal';

const SavedFiltersView = ({ onClose, onFilterSelect, jobs = [], darkMode = false }) => {
  const [savedFilters, setSavedFilters] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showRankModal, setShowRankModal] = useState(false);

  useEffect(() => {
    loadSavedFilters();
  }, []);

  const loadSavedFilters = () => {
    const filters = JSON.parse(localStorage.getItem('savedCandidateFilters') || '[]');
    filters.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    setSavedFilters(filters);
  };

  const handleFilterSelect = (filter) => {
    setSelectedFilter(filter);
    setFilteredCandidates(filter.candidates || []);
  };

  const handleRefreshFilter = async () => {
    if (!selectedFilter) return;

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');

      // Fetch all candidates
      const response = await axios.get(`${API_URL}/api/candidates/`, {
        headers: { Authorization: `Token ${token}` }
      });

      console.log('DEBUG - All candidates from API:', response.data.length);

      // FAILSAFE MODE: Just keep the existing candidates instead of trying to refilter
      // This way the filters will be preserved visually
      setFilteredCandidates(selectedFilter.candidates || []);

      // Update the lastRefreshed timestamp to show the user that something happened
      const updatedFilter = { 
        ...selectedFilter,
        lastRefreshed: new Date().toISOString()
      };

      // Update the local state
      setSelectedFilter(updatedFilter);

      // Update the saved filters state
      const updatedFilters = savedFilters.map(f => 
        f.id === selectedFilter.id ? updatedFilter : f
      );
      setSavedFilters(updatedFilters);

      // Update localStorage
      localStorage.setItem('savedCandidateFilters', JSON.stringify(updatedFilters));

      // Create a temporary message to explain the workaround to the user
      const tempElement = document.createElement('div');
      tempElement.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #4CAF50;
        color: white;
        padding: 16px;
        border-radius: 4px;
        z-index: 9999;
        max-width: 300px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      `;
      tempElement.innerHTML = '<p><strong>Refiltering temporarily disabled</strong></p><p>To see updated candidates, please create a new filter.</p>';
      document.body.appendChild(tempElement);

      // Remove the message after 5 seconds
      setTimeout(() => {
        if (document.body.contains(tempElement)) {
          document.body.removeChild(tempElement);
        }
      }, 5000);

    } catch (error) {
      console.error('Error refreshing filter:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteFilter = (filterId) => {
    const updatedFilters = savedFilters.filter(f => f.id !== filterId);
    localStorage.setItem('savedCandidateFilters', JSON.stringify(updatedFilters));
    setSavedFilters(updatedFilters);

    if (selectedFilter && selectedFilter.id === filterId) {
      setSelectedFilter(null);
      setFilteredCandidates([]);
    }
  };

  // Helper function to get hiring status label from value
  const getStatusLabel = (statusValue) => {
    const statusMap = {
      'no_engagement': 'No Engagement',
      'l1_scheduled': 'L1 Scheduled',
      'l1_attended': 'L1 Attended',
      'l1_dropoff': 'L1 Dropoff',
      'l1_hold': 'L1 Hold',
      'l1_rejected': 'L1 Rejected',
      'l1_selected': 'L1 Selected',
      'l2_scheduled': 'L2 Scheduled',
      'l2_attended': 'L2 Attended',
      'l2_dropoff': 'L2 Dropoff',
      'l2_hold': 'L2 Hold',
      'l2_rejected': 'L2 Rejected',
      'l2_selected': 'L2 Selected',
      'l3_scheduled': 'L3 Scheduled',
      'l3_attended': 'L3 Attended',
      'l3_dropoff': 'L3 Dropoff',
      'l3_hold': 'L3 Hold',
      'l3_rejected': 'L3 Rejected',
      'l3_selected': 'L3 Selected'
    };
    return statusMap[statusValue] || statusValue;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg w-full max-w-5xl h-[90vh] flex flex-col`}>
        <h2 className="text-xl font-semibold mb-4">Saved Filters</h2>

        <div className="flex flex-grow overflow-hidden">
          {/* Sidebar with saved filters */}
          <div className={`w-1/4 pr-4 mr-4 border-r ${darkMode ? 'border-gray-600' : 'border-gray-200'} overflow-y-auto`}>
            <h3 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
              Your Filters ({savedFilters.length})
            </h3>
            
            {savedFilters.length === 0 ? (
              <div className={`p-4 ${darkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-50 text-gray-500'} rounded text-center`}>
                <p>No saved filters yet</p>
              </div>
            ) : (
              <div className="space-y-2">
                {savedFilters.map(filter => (
                  <div 
                    key={filter.id}
                    className={`p-3 rounded cursor-pointer transition-colors ${
                      selectedFilter && selectedFilter.id === filter.id
                        ? darkMode 
                          ? 'bg-blue-700 text-white' 
                          : 'bg-blue-50 border-blue-200 text-blue-800'
                        : darkMode
                          ? 'bg-gray-700 hover:bg-gray-600 text-white border-gray-600'
                          : 'bg-white hover:bg-gray-50 border-gray-200'
                    } border shadow-sm`}
                    onClick={() => handleFilterSelect(filter)}
                  >
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium">{filter.name}</h4>
                      <button 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteFilter(filter.id);
                        }}
                        className={`text-sm px-1 ${darkMode ? 'text-gray-400 hover:text-red-300' : 'text-gray-400 hover:text-red-500'}`}
                        title="Delete filter"
                      >
                        ×
                      </button>
                    </div>
                    
                    <div className={`mt-1 text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Created: {new Date(filter.createdAt).toLocaleDateString()}
                    </div>
                    
                    {filter.lastRefreshed && (
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        Refreshed: {new Date(filter.lastRefreshed).toLocaleDateString()}
                      </div>
                    )}
                    
                    <div className={`mt-1 text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {filter.candidates ? filter.candidates.length : 0} candidates
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Main content area */}
          <div className="flex-grow overflow-hidden flex flex-col">
            {selectedFilter ? (
              <>
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-semibold">{selectedFilter.name}</h3>
                  <div>
                    <button
                      onClick={() => setShowRankModal(true)}
                      className="px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm mr-2"
                    >
                      Rank
                    </button>
                    <button
                      onClick={() => handleRefreshFilter()}
                      className={`px-3 py-1 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''} bg-blue-600 text-white rounded hover:bg-blue-700 text-sm`}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Refiltering...' : 'Refilter'}
                    </button>
                  </div>
                </div>

                <div className={`p-4 mt-2 ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} border rounded-lg`}>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Job Filter</h4>
                      {selectedFilter.jobId ? (
                        <div className={`p-2 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded shadow-sm`}>
                          <p className="font-medium">
                            {jobs.find(job => job.id === selectedFilter.jobId)?.title || 'Unknown Job'}
                          </p>
                        </div>
                      ) : (
                        <p className={`italic ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No job filter applied</p>
                      )}
                    </div>
                    
                    <div>
                      <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Hiring Status Filters</h4>
                      {selectedFilter.statuses && selectedFilter.statuses.length > 0 ? (
                        <div className={`p-2 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded shadow-sm`}>
                          <div className="flex flex-wrap gap-1">
                            {selectedFilter.statuses.map(status => (
                              <span 
                                key={status} 
                                className={`px-2 py-0.5 text-xs rounded-full ${darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-50 text-blue-700'}`}
                              >
                                {getStatusLabel(status)}
                              </span>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <p className={`italic ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No hiring status filters applied</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="overflow-y-auto flex-grow mt-2">
                  <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                    Matched Candidates ({filteredCandidates.length})
                  </h4>

                  {isLoading ? (
                    <div className={`${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-50 text-gray-500'} p-4 rounded text-center`}>
                      <p>Refiltering candidates...</p>
                    </div>
                  ) : filteredCandidates.length === 0 ? (
                    <div className={`${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-50 text-gray-500'} p-4 rounded text-center`}>
                      <p>No matching candidates found</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredCandidates.map(candidate => (
                        <div 
                          key={candidate.id}
                          className={`p-3 rounded shadow-sm border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}
                        >
                          <div className="flex justify-between">
                            <h4 className="font-medium">{candidate.name || "Unnamed Candidate"}</h4>
                            <span className={`px-2 py-0.5 text-xs rounded-full ${darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'}`}>
                              {candidate.total_experience ? `${candidate.total_experience} Yrs` : 'No Exp'}
                            </span>
                          </div>
                          <div className="mt-1 grid grid-cols-2 gap-x-2 text-sm">
                            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              <span className="font-medium">Role:</span> {candidate.preferred_role_display}
                            </p>
                            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              <span className="font-medium">Status:</span> {candidate.hiring_status_display}
                            </p>
                          </div>
                          {candidate.comments && (
                            <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300 bg-gray-800' : 'text-gray-600 bg-gray-50'} p-2 rounded`}>
                              {candidate.comments}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className={`flex items-center justify-center h-full ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Select a filter to view candidates
              </div>
            )}
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            onClick={onClose}
            className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
          >
            Close
          </button>
        </div>
      </div>

      {/* Rank Candidates Modal */}
      {showRankModal && selectedFilter && (
        <RankCandidatesModal
          isOpen={showRankModal}
          onClose={() => setShowRankModal(false)}
          jobId={selectedFilter.jobId}
          jobTitle={jobs.find(job => job.id === selectedFilter.jobId)?.title || 'Job'}
          candidateIds={filteredCandidates.map(candidate => candidate.id)}
          candidates={filteredCandidates}
          darkMode={darkMode}
        />
      )}
    </div>
  );
};

export default SavedFiltersView;
