# Generated by Django 5.2 on 2025-04-13 20:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('candidates', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='candidate',
            name='comments',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='candidate',
            name='hiring_status',
            field=models.CharField(choices=[('no_engagement', 'No Engagement'), ('l1_scheduled', 'L1 Scheduled'), ('l1_attended', 'L1 Attended'), ('l1_dropoff', 'L1 Dropoff'), ('l1_hold', 'L1 Hold'), ('l1_rejected', 'L1 Rejected'), ('l1_selected', 'L1 Selected'), ('l2_scheduled', 'L2 Scheduled'), ('l2_attended', 'L2 Attended'), ('l2_dropoff', 'L2 Dropoff'), ('l2_hold', 'L2 Hold'), ('l2_rejected', 'L2 Rejected'), ('l2_selected', 'L2 Selected'), ('l3_scheduled', 'L3 Scheduled'), ('l3_attended', 'L3 Attended'), ('l3_dropoff', 'L3 Dropoff'), ('l3_hold', 'L3 Hold'), ('l3_rejected', 'L3 Rejected'), ('l3_selected', 'L3 Selected')], default='no_engagement', max_length=50),
        ),
    ]
