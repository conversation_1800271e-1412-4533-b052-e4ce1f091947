import React, { useState } from 'react';
import FilterCandidatesModal from './FilterCandidatesModal';
import QAGeneratorModal from './QAGeneratorModal';

const JobCard = ({ job, onEdit, onDelete, onSaveFilter, darkMode = false, isSelected = false, onSelect }) => {
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [showDocument, setShowDocument] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showQAModal, setShowQAModal] = useState(false);
  
  const getStatusColor = (status) => {
    if (darkMode) {
      switch (status) {
        case 'active':
          return 'bg-green-900 text-green-200';
        case 'hold':
          return 'bg-yellow-900 text-yellow-200';
        case 'stopped':
          return 'bg-red-900 text-red-200';
        default:
          return 'bg-gray-700 text-gray-200';
      }
    } else {
      switch (status) {
        case 'active':
          return 'bg-green-100 text-green-800';
        case 'hold':
          return 'bg-yellow-100 text-yellow-800';
        case 'stopped':
          return 'bg-red-100 text-red-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    }
  };

  const handleDeleteClick = () => {
    setShowConfirmDelete(true);
  };

  const handleConfirmDelete = () => {
    onDelete(job.id);
    setShowConfirmDelete(false);
  };

  const handleCancelDelete = () => {
    setShowConfirmDelete(false);
  };
  
  const handleDownloadDocument = () => {
    // Create a temporary link and trigger download
    if (job.description_document_url) {
      window.open(job.description_document_url, '_blank');
    }
  };

  const handleFilterCandidates = () => {
    setShowFilterModal(true);
  };

  const handleCloseFilterModal = () => {
    setShowFilterModal(false);
  };

  const handleSaveFilter = (filterData) => {
    if (onSaveFilter) {
      onSaveFilter(filterData);
    }
  };
  
  const handleOpenQAModal = () => {
    setShowQAModal(true);
  };
  
  const handleCloseQAModal = () => {
    setShowQAModal(false);
  };

  return (
    <div className={`${darkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white text-gray-900'} rounded-lg shadow overflow-hidden ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
      <div className={`${darkMode ? 'border-gray-700' : 'border-gray-200'} border-b p-4 flex justify-between items-start`}>
        <div className="flex items-start">
          {onSelect && (
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onSelect(job.id)}
              className="mr-3 mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
          )}
          <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {job.title_display || 'Untitled Job'}
          </h3>
          <div className="mt-2 flex items-center text-sm text-gray-500">
            {job.positions_available && (
              <div className={`mr-4 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                <span className="font-medium">{job.positions_available}</span> openings
              </div>
            )}
            {job.created_on && (
              <div className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                Created: {new Date(job.created_on).toLocaleDateString()}
              </div>
            )}
          </div>
          </div>
        </div>
        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(job.recruitment_status)}`}>
          {job.recruitment_status_display}
        </span>
      </div>
      
      <div className="px-4 py-3">
        <div className="grid grid-cols-2 gap-4">
          {job.hiring_manager && (
            <div>
              <h4 className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>HIRING MANAGER</h4>
              <p className={`mt-1 text-sm ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{job.hiring_manager}</p>
            </div>
          )}
          
          {job.ta_incharge && (
            <div>
              <h4 className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>TA INCHARGE</h4>
              <p className={`mt-1 text-sm ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{job.ta_incharge}</p>
            </div>
          )}
          
          {job.hiring_team && (
            <div className="col-span-2">
              <h4 className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>HIRING TEAM</h4>
              <p className={`mt-1 text-sm ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{job.hiring_team}</p>
            </div>
          )}
          {job.description_document_filename && (
            <div className="col-span-2">
              <h4 className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>DOCUMENT</h4>
              <p className={`mt-1 text-sm ${darkMode ? 'text-gray-100' : 'text-gray-900'} flex items-center`}>
                <span className="truncate flex-1">{job.description_document_filename}</span>
                <button 
                  onClick={handleDownloadDocument}
                  className={`ml-2 p-1 ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}
                  title="JD Download"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
      
      <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} px-4 py-3 flex justify-between text-sm`}>
        <div className={`flex items-center ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
          <span>
            Added by {job.created_by_username || 'Unknown'}
          </span>
        </div>
        <div className="flex space-x-4">
          <button 
            onClick={handleOpenQAModal}
            className={`${darkMode ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-900'} font-medium`}
          >
            Q &amp; A
          </button>
          <button 
            onClick={handleFilterCandidates}
            className={`${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-900'} font-medium`}
          >
            Filter Candidates
          </button>
          <button 
            onClick={() => onEdit(job)}
            className={`${darkMode ? 'text-indigo-400 hover:text-indigo-300' : 'text-indigo-600 hover:text-indigo-900'} font-medium`}
          >
            Edit
          </button>
          <button 
            onClick={handleDeleteClick}
            className={`${darkMode ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-900'} font-medium`}
          >
            Delete
          </button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg max-w-sm mx-auto`}>
            <h3 className="text-lg font-semibold mb-4">Delete Confirmation</h3>
            <p>Are you sure you want to delete this job?</p>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={handleCancelDelete}
                className={`px-4 py-2 ${darkMode ? 'bg-gray-600 text-gray-100' : 'bg-gray-200 text-gray-800'} rounded hover:bg-gray-300`}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filter Candidates Modal */}
      <FilterCandidatesModal 
        job={job}
        isOpen={showFilterModal}
        onClose={handleCloseFilterModal}
        onSaveFilter={handleSaveFilter}
        darkMode={darkMode}
      />
      
      {/* Q&A Generator Modal */}
      <QAGeneratorModal
        job={job}
        isOpen={showQAModal}
        onClose={handleCloseQAModal}
        darkMode={darkMode}
      />
    </div>
  );
};

export default JobCard;
