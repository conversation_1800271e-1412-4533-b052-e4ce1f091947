import React, { useState } from 'react';

const FileUpload = ({ onFileSelect, label = "Upload File", acceptTypes, darkMode = false }) => {
  const [fileName, setFileName] = useState('');

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFileName(file.name);
      onFileSelect(file);
    }
  };

  return (
    <div className="mt-2">
      <div className="flex items-center">
        <label className={`cursor-pointer inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium 
          ${darkMode 
            ? 'border-gray-600 text-gray-200 bg-gray-700 hover:bg-gray-600'
            : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
          } focus:outline-none`}>
          <svg className={`h-5 w-5 mr-2 ${darkMode ? 'text-gray-300' : 'text-gray-400'}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Choose File
          <input
            type="file"
            className="sr-only"
            onChange={handleFileChange}
            accept={acceptTypes}
          />
        </label>
        <span className={`ml-3 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-500'} truncate max-w-xs`}>
          {fileName || 'No file selected'}
        </span>
      </div>
    </div>
  );
};

export default FileUpload;
