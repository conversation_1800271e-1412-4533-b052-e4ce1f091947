import React, { useState, useEffect } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import FileUpload from './FileUpload';
import axios from 'axios';

const EditJobModal = ({ isOpen, onClose, onJobUpdated, job, customJobTitles, darkMode = false }) => {
  const [file, setFile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [jobTitleOptions, setJobTitleOptions] = useState([]);

  useEffect(() => {
    if (isOpen && job) {
      loadJobTitleOptions();
    }
  }, [isOpen, job]);

  const loadJobTitleOptions = () => {
    let options;
    
    // If customJobTitles prop is provided, use it
    if (customJobTitles && customJobTitles.length > 0) {
      options = customJobTitles;
    } else {
      // Otherwise, try to get from localStorage
      const savedOptions = JSON.parse(localStorage.getItem('customJobTitles') || '[]');
      
      // If localStorage has options, use them
      if (savedOptions.length > 0) {
        options = savedOptions;
      } else {
        // Fallback to default options
        options = [
          { value: 'devops', label: 'DevOps Engineer' },
          { value: 'data_analyst', label: 'Data Analyst' },
          { value: 'qa_testing', label: 'QA Testing Engineer' },
          { value: 'java_fullstack', label: 'Java Full Stack Engineer' },
          { value: 'python_developer', label: 'Python Developer' },
          { value: 'servicenow', label: 'ServiceNow Specialist' },
          { value: 'rpa_developer', label: 'RPA Developer' }
        ];
      }
    }
    
    setJobTitleOptions(options);
  };

  // Don't render the modal if job is null
  if (!job) return null;

  const initialValues = {
    title: job.title || '',
    positions_available: job.positions_available || '',
    hiring_team: job.hiring_team || '',
    hiring_manager: job.hiring_manager || '',
    ta_incharge: job.ta_incharge || '',
    created_on: job.created_on || new Date().toISOString().split('T')[0],
    recruitment_status: job.recruitment_status || 'active'
  };

  const handleSubmit = async (values) => {
    setIsSubmitting(true);
    setError('');
    
    try {
      // Create form data for file upload
      const formData = new FormData();
      
      // Add all fields to form data
      Object.keys(values).forEach(key => {
        if (values[key] !== null && values[key] !== undefined) {
          formData.append(key, values[key]);
        }
      });
      
      // Add file if it exists
      if (file) {
        formData.append('description_document', file);
      }
      
      // Get token from localStorage
      const token = localStorage.getItem('token');
      
      // Send request to update job
      const response = await axios.patch(`http://localhost:8000/api/jobs/${job.id}/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Token ${token}`
        }
      });
      
      // Call onJobUpdated with the updated job data
      onJobUpdated(response.data);
      
      // Close the modal
      onClose();
    } catch (err) {
      console.error('Error updating job:', err);
      setError(err.response?.data?.message || 'Failed to update job');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Only render if isOpen is true
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className={`relative top-20 mx-auto p-5 border w-full max-w-xl shadow-lg rounded-md ${darkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white border-gray-300'}`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Edit Job</h3>
          <button 
            onClick={onClose}
            className={`${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-500'}`}
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {error && (
          <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'} p-3 rounded-md mb-4`}>
            {error}
          </div>
        )}
        
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting: formikSubmitting }) => (
            <Form className="space-y-4">
              <div>
                <label htmlFor="title" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Job Title</label>
                <Field
                  as="select"
                  name="title"
                  id="title"
                  className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                >
                  <option value="">Select Job Title</option>
                  {jobTitleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </Field>
              </div>
              
              <div>
                <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Description Document
                </label>
                <FileUpload 
                  onFileChange={setFile} 
                  label="Upload JD (PDF, DOC, DOCX)" 
                  acceptTypes=".pdf,.doc,.docx"
                  darkMode={darkMode}
                />
              </div>
              
              {job.description_document && (
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Current file: {job.description_document.split('/').pop()}
                </div>
              )}
              
              <div>
                <label htmlFor="positions_available" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Positions Available</label>
                <Field
                  type="number"
                  name="positions_available"
                  id="positions_available"
                  min="1"
                  className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                />
              </div>
              
              <div>
                <label htmlFor="hiring_team" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Hiring Team</label>
                <Field
                  type="text"
                  name="hiring_team"
                  id="hiring_team"
                  className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="hiring_manager" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Hiring Manager</label>
                  <Field
                    type="text"
                    name="hiring_manager"
                    id="hiring_manager"
                    className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                  />
                </div>
                
                <div>
                  <label htmlFor="ta_incharge" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>TA Incharge</label>
                  <Field
                    type="text"
                    name="ta_incharge"
                    id="ta_incharge"
                    className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="created_on" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Position Created On</label>
                <Field
                  type="date"
                  name="created_on"
                  id="created_on"
                  className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                />
              </div>
              
              <div>
                <label htmlFor="recruitment_status" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Recruitment Status</label>
                <Field
                  as="select"
                  name="recruitment_status"
                  id="recruitment_status"
                  className={`mt-1 block w-full shadow-sm sm:text-sm ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded-md`}
                >
                  <option value="active">Actively Recruiting</option>
                  <option value="hold">On Hold</option>
                  <option value="stopped">Stopped</option>
                </Field>
              </div>
              
              <div className={`pt-4 flex justify-end space-x-3 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <button
                  type="button"
                  onClick={onClose}
                  className={`py-2 px-4 border rounded-md shadow-sm text-sm font-medium ${darkMode ? 'bg-gray-700 text-gray-200 border-gray-600 hover:bg-gray-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'} focus:outline-none`}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || formikSubmitting}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none disabled:opacity-50"
                >
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default EditJobModal;
