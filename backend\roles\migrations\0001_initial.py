# Generated migration for roles app

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.Char<PERSON>ield(help_text='System identifier (snake_case)', max_length=50, unique=True)),
                ('label', models.CharField(help_text='Human-readable display name', max_length=100)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Whether this role is available for selection')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['label'],
            },
        ),
    ]
