from django.db import models
from django.conf import settings
import os
from datetime import datetime

# Create your models here.

def resume_upload_path(instance, filename):
    """
    Generate a path for uploading candidate resumes
    Stores in: media/candidate_resumes/[filename]
    """
    return os.path.join('candidate_resumes', filename)

def transcript_upload_path(instance, filename):
    """
    Generate a path for uploading interview transcripts
    Stores in: media/interview_transcripts/candidate_{id}_level_{level}_{filename}
    This flatter structure avoids potential path issues while keeping files organized
    """
    # Extract file extension
    _, ext = os.path.splitext(filename)

    # Create a more identifiable filename with timestamp to avoid collisions
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    new_filename = f"candidate_{instance.candidate.id}_level_{instance.level}_{timestamp}{ext}"

    return os.path.join('interview_transcripts', new_filename)

def evaluation_report_path(instance, filename):
    """
    Generate a path for storing evaluation reports
    Stores in: media/evaluation_reports/candidate_[id]/[level]/[filename]
    """
    return os.path.join(
        'evaluation_reports',
        f'candidate_{instance.candidate.id}',
        f'level_{instance.level}',
        filename
    )

class Candidate(models.Model):
    # Legacy ROLE_CHOICES kept for migration compatibility
    ROLE_CHOICES = (
        ('devops', 'DevOps Engineer'),
        ('data_analyst', 'Data Analyst'),
        ('qa_testing', 'QA Testing Engineer'),
        ('java_fullstack', 'Java Full Stack Engineer'),
        ('python_developer', 'Python Developer'),
        ('servicenow', 'ServiceNow Specialist'),
        ('rpa_developer', 'RPA Developer'),
    )

    HIRING_STATUS_CHOICES = (
        ('no_engagement', 'No Engagement'),
        ('l1_scheduled', 'L1 Scheduled'),
        ('l1_attended', 'L1 Attended'),
        ('l1_dropoff', 'L1 Dropoff'),
        ('l1_hold', 'L1 Hold'),
        ('l1_rejected', 'L1 Rejected'),
        ('l1_selected', 'L1 Selected'),
        ('l2_scheduled', 'L2 Scheduled'),
        ('l2_attended', 'L2 Attended'),
        ('l2_dropoff', 'L2 Dropoff'),
        ('l2_hold', 'L2 Hold'),
        ('l2_rejected', 'L2 Rejected'),
        ('l2_selected', 'L2 Selected'),
        ('l3_scheduled', 'L3 Scheduled'),
        ('l3_attended', 'L3 Attended'),
        ('l3_dropoff', 'L3 Dropoff'),
        ('l3_hold', 'L3 Hold'),
        ('l3_rejected', 'L3 Rejected'),
        ('l3_selected', 'L3 Selected'),
    )

    L1_STATUS_CHOICES = (
        ('scheduled', 'Scheduled'),
        ('attended', 'Attended'),
        ('hold', 'Hold'),
        ('rejected', 'Rejected'),
        ('selected', 'Selected'),
        ('dropoff', 'DropOff'),
    )

    name = models.CharField(max_length=100, blank=True, null=True)
    candidate_id = models.CharField(max_length=50, blank=True, null=True, help_text="Unique candidate identifier")
    primary_email = models.EmailField(blank=True, null=True, help_text="Primary email address")
    mobile = models.CharField(max_length=20, blank=True, null=True, help_text="Mobile phone number")
    spoc = models.CharField(max_length=100, blank=True, null=True, help_text="Single Point of Contact")

    # Role fields - temporarily without choices constraint for dynamic role support
    # This allows any string value to be stored, enabling dynamic role creation
    preferred_role = models.CharField(max_length=50, blank=True, null=True, help_text="Role identifier")
    optional_roles = models.CharField(max_length=50, blank=True, null=True, help_text="Alternative role identifier")
    resume = models.FileField(upload_to=resume_upload_path, blank=True, null=True)
    total_experience = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)  # In years, e.g. 5.5 years
    last_job_date = models.DateField(blank=True, null=True)
    hiring_status = models.CharField(max_length=50, choices=HIRING_STATUS_CHOICES, default='no_engagement')

    # L1 Interview Details
    l1_status = models.CharField(max_length=20, choices=L1_STATUS_CHOICES, blank=True, null=True, help_text="L1 interview status")
    l1_schedule_date = models.DateField(blank=True, null=True, help_text="L1 interview scheduled date")
    l1_schedule_time = models.TimeField(blank=True, null=True, help_text="L1 interview scheduled time")
    l1_panel_name = models.CharField(max_length=100, blank=True, null=True, help_text="L1 interview panel member name")
    l1_panel_comment = models.TextField(blank=True, null=True, help_text="L1 panel comments and score")

    jira_tickets = models.CharField(max_length=255, blank=True, null=True, help_text="Comma-separated list of Jira ticket numbers")
    comments = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_candidates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name or "Unnamed Candidate"

    @property
    def resume_filename(self):
        """Return just the filename of the resume, not the full path"""
        if self.resume:
            return os.path.basename(self.resume.name)
        return None

class CandidateRanking(models.Model):
    """Store rankings for candidates against specific jobs"""
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='rankings')
    job = models.ForeignKey('jobs.Job', on_delete=models.CASCADE, related_name='candidate_rankings')
    score = models.IntegerField(default=0)  # Score from 1-10
    reasoning = models.TextField(blank=True)  # AI explanation
    matching_skills = models.JSONField(default=list, blank=True)  # JSON array of matching skills
    missing_skills = models.JSONField(default=list, blank=True)  # JSON array of missing skills
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('candidate', 'job')  # One ranking per candidate-job pair
        ordering = ['-score', '-updated_at']  # Order by score (highest first) then most recent

    def __str__(self):
        return f"{self.candidate.name} - {self.job.title} - Score: {self.score}"

class InterviewTranscript(models.Model):
    """Store interview transcripts for candidates at different levels"""
    LEVEL_CHOICES = (
        ('L1', 'Level 1'),
        ('L2', 'Level 2'),
        ('L3', 'Level 3'),
        ('L4', 'Level 4'),
    )

    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='transcripts')
    level = models.CharField(max_length=2, choices=LEVEL_CHOICES)
    transcript_file = models.FileField(upload_to=transcript_upload_path)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('candidate', 'level')  # One transcript per level per candidate

    def __str__(self):
        return f"{self.candidate.name} - {self.get_level_display()} Transcript"

    @property
    def filename(self):
        """Return just the filename of the transcript, not the full path"""
        if self.transcript_file:
            return os.path.basename(self.transcript_file.name)
        return None

class InterviewLevel(models.Model):
    """Store interview level details for candidates"""
    LEVEL_STATUS_CHOICES = (
        ('scheduled', 'Scheduled'),
        ('attended', 'Attended'),
        ('hold', 'Hold'),
        ('rejected', 'Rejected'),
        ('selected', 'Selected'),
        ('dropoff', 'DropOff'),
    )

    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE, related_name='interview_levels')
    level_name = models.CharField(max_length=10, help_text="Level name (L1, L2, L3, etc.)")
    level_order = models.IntegerField(help_text="Order of the level (1, 2, 3, etc.)")
    status = models.CharField(max_length=20, choices=LEVEL_STATUS_CHOICES, blank=True, null=True)
    schedule_date = models.DateField(blank=True, null=True)
    schedule_time = models.TimeField(blank=True, null=True)
    panel_name = models.CharField(max_length=100, blank=True, null=True)
    panel_comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('candidate', 'level_name')
        ordering = ['level_order']

    def __str__(self):
        return f"{self.candidate.name} - {self.level_name}"

class TranscriptEvaluation(models.Model):
    """Store evaluation results for interview transcripts"""
    transcript = models.OneToOneField(InterviewTranscript, on_delete=models.CASCADE, related_name='evaluation')
    score = models.IntegerField(default=0)  # Score from 0-10
    technical_strengths = models.TextField(blank=True)  # New field for technical strengths
    improvement_areas = models.TextField(blank=True)
    plagiarism_concerns = models.TextField(blank=True)
    detailed_report = models.TextField(blank=True)
    report_file = models.FileField(upload_to=evaluation_report_path, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Evaluation for {self.transcript}"

    @property
    def report_filename(self):
        """Return just the filename of the report, not the full path"""
        if self.report_file:
            return os.path.basename(self.report_file.name)
        return None
