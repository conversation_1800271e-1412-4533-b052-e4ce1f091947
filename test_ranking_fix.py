#!/usr/bin/env python3
"""
Test script to verify the ranking functionality fixes.
This script tests the key areas where job.title access was causing issues.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')
django.setup()

from jobs.models import Job
from candidates.models import Candidate, CandidateRanking
from candidates.ranking import rank_candidates_for_job
from candidates.serializers import CandidateRankingSerializer

def test_job_title_access():
    """Test that job title access works correctly even with null titles."""
    print("Testing job title access...")
    
    # Test with jobs that might have null titles
    jobs = Job.objects.all()[:5]  # Get first 5 jobs
    
    for job in jobs:
        print(f"Job ID: {job.id}")
        print(f"  Raw title: {job.title}")
        print(f"  Title display: {job.get_title_display()}")
        print(f"  String representation: {job}")
        
        # Test CandidateRanking string representation
        rankings = CandidateRanking.objects.filter(job=job)[:1]
        if rankings:
            ranking = rankings[0]
            print(f"  Ranking string: {ranking}")
            
            # Test serializer
            serializer = CandidateRankingSerializer(ranking)
            print(f"  Serialized job title: {serializer.data.get('job_title')}")
        
        print("---")

def test_ranking_functionality():
    """Test the ranking functionality with the fixes."""
    print("\nTesting ranking functionality...")
    
    # Get a job and some candidates
    job = Job.objects.first()
    candidates = Candidate.objects.all()[:2]  # Get first 2 candidates
    
    if job and candidates:
        print(f"Testing ranking for job: {job.get_title_display() or 'Untitled Job'}")
        print(f"With {len(candidates)} candidates")
        
        try:
            # This should not crash anymore
            rankings = rank_candidates_for_job(job, candidates, force_refresh=False)
            print(f"Successfully generated {len(rankings)} rankings")
            
            for ranking in rankings:
                print(f"  - {ranking['candidate_name']}: {ranking['score']}")
                
        except Exception as e:
            print(f"Error in ranking: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("No job or candidates found for testing")

if __name__ == "__main__":
    print("Testing ranking functionality fixes...")
    print("=" * 50)
    
    test_job_title_access()
    test_ranking_functionality()
    
    print("\nTest completed!")
