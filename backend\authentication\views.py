from django.shortcuts import render
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from .serializers import UserSerializer, AdminUserCreationSerializer

User = get_user_model()

class CustomAuthToken(ObtainAuthToken):
    """
    Custom token authentication for users with extended response
    """
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)

        # Make sure admin status is correctly set
        is_admin = user.is_admin or user.is_staff or user.is_superuser

        return Response({
            'token': token.key,
            'user_id': user.pk,
            'email': user.email,
            'username': user.username,
            'is_admin': is_admin,
            'is_superuser': user.is_superuser,
        })

class LogoutView(APIView):
    """
    Logout view to invalidate auth token
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # Delete the user's token to logout
        request.user.auth_token.delete()
        return Response({"message": "Successfully logged out"}, status=status.HTTP_200_OK)

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def register_user(request):
    """
    Register a new user
    """
    serializer = UserSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user': UserSerializer(user).data
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AdminUserCreationView(APIView):
    """
    View for admin to create new users
    - Superusers can create both normal users and admins
    - Admins can only create normal users
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        # Check if user is admin or superuser
        if not request.user.is_admin and not request.user.is_staff and not request.user.is_superuser:
            return Response({"detail": "You do not have permission to perform this action"},
                          status=status.HTTP_403_FORBIDDEN)

        # If user is admin but not superuser, they can't create admin users
        if request.user.is_admin and not request.user.is_superuser and request.data.get('is_admin', False):
            return Response({"detail": "Only superusers can create admin users"},
                          status=status.HTTP_403_FORBIDDEN)

        # If user is admin but not superuser, they can't create superusers
        if request.user.is_admin and not request.user.is_superuser and request.data.get('is_superuser', False):
            return Response({"detail": "Only superusers can create superuser accounts"},
                          status=status.HTTP_403_FORBIDDEN)

        serializer = AdminUserCreationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response(UserSerializer(user).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserListView(APIView):
    """
    View for admin to list all users
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # Check if user is admin
        if not request.user.is_admin and not request.user.is_staff:
            return Response({"detail": "You do not have permission to perform this action"},
                           status=status.HTTP_403_FORBIDDEN)

        users = User.objects.all().order_by('username')  # Sort by username ascending
        serializer = UserSerializer(users, many=True)
        return Response(serializer.data)

@api_view(['PATCH'])
@permission_classes([permissions.IsAuthenticated])
def bulk_deactivate_users(request):
    """
    Bulk deactivate users (admin only)
    """
    try:
        print(f"Bulk deactivate request from user: {request.user.email}")
        print(f"User is_admin: {request.user.is_admin}")
        print(f"User is_staff: {request.user.is_staff}")
        print(f"Request data: {request.data}")

        # Check if user is admin
        if not request.user.is_admin and not request.user.is_staff:
            return Response({"detail": "You do not have permission to perform this action"},
                           status=status.HTTP_403_FORBIDDEN)

        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response({"detail": "No user IDs provided"}, status=status.HTTP_400_BAD_REQUEST)

        # Prevent deactivating self
        if request.user.id in user_ids:
            return Response({"detail": "You cannot deactivate your own account"},
                           status=status.HTTP_400_BAD_REQUEST)

        print(f"Attempting to deactivate users with IDs: {user_ids}")
        updated_count = User.objects.filter(id__in=user_ids).update(is_active=False)
        print(f"Successfully deactivated {updated_count} users")
        return Response({"message": f"{updated_count} user(s) deactivated successfully"})
    except Exception as e:
        print(f"Error in bulk_deactivate_users: {str(e)}")
        import traceback
        traceback.print_exc()
        return Response({"detail": f"Failed to deactivate users: {str(e)}"},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def bulk_delete_users(request):
    """
    Bulk delete users (admin only)
    """
    try:
        print(f"Bulk delete request from user: {request.user.email}")
        print(f"User is_admin: {request.user.is_admin}")
        print(f"User is_staff: {request.user.is_staff}")
        print(f"Request data: {request.data}")

        # Check if user is admin
        if not request.user.is_admin and not request.user.is_staff:
            return Response({"detail": "You do not have permission to perform this action"},
                           status=status.HTTP_403_FORBIDDEN)

        user_ids = request.data.get('user_ids', [])
        if not user_ids:
            return Response({"detail": "No user IDs provided"}, status=status.HTTP_400_BAD_REQUEST)

        # Prevent deleting self
        if request.user.id in user_ids:
            return Response({"detail": "You cannot delete your own account"},
                           status=status.HTTP_400_BAD_REQUEST)

        print(f"Attempting to delete users with IDs: {user_ids}")
        deleted_count, _ = User.objects.filter(id__in=user_ids).delete()
        print(f"Successfully deleted {deleted_count} users")
        return Response({"message": f"{deleted_count} user(s) deleted successfully"})
    except Exception as e:
        print(f"Error in bulk_delete_users: {str(e)}")
        import traceback
        traceback.print_exc()
        return Response({"detail": f"Failed to delete users: {str(e)}"},
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)
