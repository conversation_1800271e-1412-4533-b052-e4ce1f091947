import React, { useState } from 'react';
import TranscriptEvaluationModal from './TranscriptEvaluationModal';

const CandidateCard = ({ candidate, onEdit, onDelete, darkMode = false }) => {
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);
  
  const handleDeleteClick = () => {
    setShowConfirmDelete(true);
  };

  const handleConfirmDelete = () => {
    onDelete(candidate.id);
    setShowConfirmDelete(false);
  };

  const handleCancelDelete = () => {
    setShowConfirmDelete(false);
  };
  
  const handleDownloadResume = () => {
    if (candidate.resume_url) {
      window.open(candidate.resume_url, '_blank');
    }
  };
  
  const getHiringStatusColor = (status) => {
    if (status.includes('rejected')) {
      return darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800';
    } else if (status.includes('selected')) {
      return darkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800';
    } else if (status.includes('hold')) {
      return darkMode ? 'bg-yellow-900 text-yellow-200' : 'bg-yellow-100 text-yellow-800';
    } else if (status.includes('scheduled')) {
      return darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800';
    } else if (status.includes('attended')) {
      return darkMode ? 'bg-purple-900 text-purple-200' : 'bg-purple-100 text-purple-800';
    } else if (status.includes('dropoff')) {
      return darkMode ? 'bg-orange-900 text-orange-200' : 'bg-orange-100 text-orange-800';
    } else {
      return darkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`p-4 ${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-md mb-4`}>
      {/* Card Header */}
      <div className="flex justify-between items-start">
        <h3 className="text-lg font-semibold">{candidate.name || "Unnamed Candidate"}</h3>
        <div className="flex flex-col items-end">
          {candidate.total_experience && (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'} mb-1`}>
              {candidate.total_experience} Years
            </span>
          )}
          {candidate.hiring_status_display && (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getHiringStatusColor(candidate.hiring_status)}`}>
              {candidate.hiring_status_display}
            </span>
          )}
        </div>
      </div>
      
      {/* Card Body */}
      <div className="mt-2 grid grid-cols-2 gap-4">
        {candidate.candidate_id && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>CANDIDATE ID</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.candidate_id}</p>
          </div>
        )}
        {candidate.primary_email && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>PRIMARY EMAIL</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.primary_email}</p>
          </div>
        )}
        {candidate.mobile && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>MOBILE</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.mobile}</p>
          </div>
        )}
        {candidate.spoc && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>SPOC</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.spoc}</p>
          </div>
        )}
        {candidate.preferred_role_display && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>TARGET ROLE</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.preferred_role_display}</p>
          </div>
        )}
        {candidate.optional_roles_display && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>OPTIONAL ROLES</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.optional_roles_display}</p>
          </div>
        )}
        {candidate.last_job_date && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>LAST JOB DATE</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{new Date(candidate.last_job_date).toLocaleDateString()}</p>
          </div>
        )}
        {/* Display Interview Levels */}
        {candidate.interview_levels && candidate.interview_levels.length > 0 && (
          candidate.interview_levels.map(level => (
            <div key={level.level_name} className="col-span-2">
              <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {level.level_name.toUpperCase()} INTERVIEW
              </p>
              <div className={`text-sm ${darkMode ? 'text-gray-200 bg-gray-700' : 'text-gray-900 bg-gray-50'} p-2 rounded mt-1`}>
                {level.status && (
                  <p><span className="font-medium">Status:</span> <span className="capitalize">{level.status}</span></p>
                )}
                {level.schedule_date && (
                  <p>
                    <span className="font-medium">Scheduled:</span> {new Date(level.schedule_date).toLocaleDateString()}
                    {level.schedule_time && ` at ${level.schedule_time}`}
                  </p>
                )}
                {level.panel_name && (
                  <p><span className="font-medium">Panel:</span> {level.panel_name}</p>
                )}
              </div>
            </div>
          ))
        )}

        {/* Fallback to legacy L1 fields if no interview_levels */}
        {(!candidate.interview_levels || candidate.interview_levels.length === 0) && candidate.l1_status && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>L1 STATUS</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'} capitalize`}>{candidate.l1_status}</p>
          </div>
        )}
        {(!candidate.interview_levels || candidate.interview_levels.length === 0) && candidate.l1_schedule_date && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>L1 SCHEDULE</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              {new Date(candidate.l1_schedule_date).toLocaleDateString()}
              {candidate.l1_schedule_time && ` at ${candidate.l1_schedule_time}`}
            </p>
          </div>
        )}
        {(!candidate.interview_levels || candidate.interview_levels.length === 0) && candidate.l1_panel_name && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>L1 PANEL</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.l1_panel_name}</p>
          </div>
        )}
        {candidate.jira_tickets && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>JIRA TICKETS</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{candidate.jira_tickets}</p>
          </div>
        )}
        {candidate.resume_filename && (
          <div>
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>RESUME</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'} flex items-center`}>
              <span className="truncate flex-1">{candidate.resume_filename}</span>
              <button
                onClick={handleDownloadResume}
                className={`ml-2 ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-800'}`}
                title="Resume Download"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
              </button>
            </p>
          </div>
        )}

        {/* Display Panel Comments from Interview Levels */}
        {candidate.interview_levels && candidate.interview_levels.length > 0 && (
          candidate.interview_levels.map(level => (
            level.panel_comment && (
              <div key={`${level.level_name}-comment`} className="col-span-2 mt-2">
                <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {level.level_name.toUpperCase()} PANEL COMMENT
                </p>
                <p className={`text-sm ${darkMode ? 'text-gray-200 bg-gray-700' : 'text-gray-900 bg-gray-50'} p-2 rounded`}>
                  {level.panel_comment}
                </p>
              </div>
            )
          ))
        )}

        {/* Fallback to legacy L1 panel comment */}
        {(!candidate.interview_levels || candidate.interview_levels.length === 0) && candidate.l1_panel_comment && (
          <div className="col-span-2 mt-2">
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>L1 PANEL COMMENT</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200 bg-gray-700' : 'text-gray-900 bg-gray-50'} p-2 rounded`}>{candidate.l1_panel_comment}</p>
          </div>
        )}

        {candidate.comments && (
          <div className="col-span-2 mt-2">
            <p className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>COMMENTS</p>
            <p className={`text-sm ${darkMode ? 'text-gray-200 bg-gray-700' : 'text-gray-900 bg-gray-50'} p-2 rounded`}>{candidate.comments}</p>
          </div>
        )}
      </div>
      
      {/* Card Actions */}
      <div className="mt-4 flex justify-end space-x-2">
        <button
          onClick={() => setShowEvaluationModal(true)}
          className="px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm"
        >
          Evaluation
        </button>
        <button
          onClick={() => onEdit(candidate)}
          className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
        >
          Edit
        </button>
        <button
          onClick={handleDeleteClick}
          className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
        >
          Delete
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg max-w-sm mx-auto`}>
            <h3 className="text-lg font-semibold mb-4">Delete Confirmation</h3>
            <p>Are you sure you want to delete this candidate?</p>
            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={handleCancelDelete}
                className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Transcript Evaluation Modal */}
      <TranscriptEvaluationModal 
        isOpen={showEvaluationModal}
        onClose={() => setShowEvaluationModal(false)}
        candidateId={candidate.id}
        candidateName={candidate.name}
        darkMode={darkMode}
      />
    </div>
  );
};

export default CandidateCard;
