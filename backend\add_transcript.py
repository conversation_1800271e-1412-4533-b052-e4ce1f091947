"""
Command-line script to add a transcript directly to the database.
This bypasses all web interfaces and directly uses Django's ORM.

Usage:
python add_transcript.py [candidate_id] [level] [pdf_path]

Example:
python add_transcript.py 1 L1 C:/path/to/transcript.pdf
"""

import os
import sys
import django
import shutil
from pathlib import Path

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')
django.setup()

from candidates.models import Candidate, InterviewTranscript
from django.conf import settings
from django.core.files import File

def add_transcript(candidate_id, level, pdf_path):
    """Add a transcript directly to the database"""
    try:
        # Validate inputs
        if level not in ['L1', 'L2', 'L3', 'L4']:
            print(f"Error: Level must be one of L1, L2, L3, L4. Got {level}")
            return False
            
        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except Candidate.DoesNotExist:
            print(f"Error: Candidate with ID {candidate_id} does not exist")
            return False
            
        if not os.path.exists(pdf_path):
            print(f"Error: File {pdf_path} does not exist")
            return False
            
        if not pdf_path.lower().endswith('.pdf'):
            print(f"Error: File {pdf_path} is not a PDF")
            return False
            
        # Check if transcript already exists
        existing = InterviewTranscript.objects.filter(candidate=candidate, level=level).first()
        if existing:
            print(f"Deleting existing transcript for {candidate.name} (Level {level})")
            if existing.transcript_file:
                if os.path.exists(existing.transcript_file.path):
                    os.remove(existing.transcript_file.path)
            existing.delete()
            
        # Create directory structure
        filename = os.path.basename(pdf_path)
        relative_path = f'interview_transcripts/candidate_{candidate.id}/level_{level}'
        full_path = os.path.join(settings.MEDIA_ROOT, relative_path)
        os.makedirs(full_path, exist_ok=True)
        
        # Copy the file to the media directory
        destination = os.path.join(full_path, filename)
        shutil.copy2(pdf_path, destination)
        
        # Create a relative path for the database
        db_path = os.path.join(relative_path, filename)
        
        # Create the transcript record
        transcript = InterviewTranscript(
            candidate=candidate,
            level=level
        )
        
        # Open the file and attach it to the model
        with open(destination, 'rb') as f:
            transcript.transcript_file.save(filename, File(f), save=False)
        
        # Save the model
        transcript.save()
        
        print(f"Successfully added transcript for {candidate.name} (Level {level})")
        print(f"File saved to: {destination}")
        return True
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python add_transcript.py [candidate_id] [level] [pdf_path]")
        sys.exit(1)
        
    candidate_id = sys.argv[1]
    level = sys.argv[2]
    pdf_path = sys.argv[3]
    
    success = add_transcript(candidate_id, level, pdf_path)
    if not success:
        sys.exit(1)
