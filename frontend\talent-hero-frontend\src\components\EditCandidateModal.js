import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import FileUpload from './FileUpload';
import InterviewLevel from './InterviewLevel';

const EditCandidateModal = ({ isOpen, onClose, onCandidateUpdated, candidate, customJobTitles, darkMode = false }) => {
  const initialCandidateData = {
    name: '',
    candidate_id: '',
    primary_email: '',
    mobile: '',
    spoc: '',
    preferred_role: '',
    optional_roles: '',
    resume: null,
    total_experience: '',
    last_job_date: '',
    hiring_status: 'no_engagement',
    l1_status: '',
    l1_schedule_date: '',
    l1_schedule_time: '',
    l1_panel_name: '',
    l1_panel_comment: '',
    jira_tickets: '',
    comments: ''
  };

  const [candidateData, setCandidateData] = useState(initialCandidateData);
  const [interviewLevels, setInterviewLevels] = useState({
    L1: { level_name: 'L1', level_order: 1, status: '', schedule_date: '', schedule_time: '', panel_name: '', panel_comment: '' }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [roleOptions, setRoleOptions] = useState([]);

  const loadCustomOptions = useCallback(() => {
    // Load custom role options
    let roles;
    if (customJobTitles && customJobTitles.length > 0) {
      roles = customJobTitles;
    } else {
      const savedRoles = JSON.parse(localStorage.getItem('customJobTitles') || '[]');
      if (savedRoles.length > 0) {
        roles = savedRoles;
      } else {
        roles = [
          { value: 'devops', label: 'DevOps Engineer' },
          { value: 'data_analyst', label: 'Data Analyst' },
          { value: 'qa_testing', label: 'QA Testing Engineer' },
          { value: 'java_fullstack', label: 'Java Full Stack Engineer' },
          { value: 'python_developer', label: 'Python Developer' },
          { value: 'servicenow', label: 'ServiceNow Specialist' },
          { value: 'rpa_developer', label: 'RPA Developer' }
        ];
      }
    }
    setRoleOptions(roles);
  }, [customJobTitles]);

  useEffect(() => {
    if (isOpen && candidate) {
      setCandidateData({
        name: candidate.name || '',
        candidate_id: candidate.candidate_id || '',
        primary_email: candidate.primary_email || '',
        mobile: candidate.mobile || '',
        spoc: candidate.spoc || '',
        preferred_role: candidate.preferred_role || '',
        optional_roles: candidate.optional_roles || '',
        resume: null, // We don't pre-populate file inputs
        total_experience: candidate.total_experience || '',
        last_job_date: candidate.last_job_date || '',
        hiring_status: candidate.hiring_status || 'no_engagement',
        l1_status: candidate.l1_status || '',
        l1_schedule_date: candidate.l1_schedule_date || '',
        l1_schedule_time: candidate.l1_schedule_time || '',
        l1_panel_name: candidate.l1_panel_name || '',
        l1_panel_comment: candidate.l1_panel_comment || '',
        jira_tickets: candidate.jira_tickets || '',
        comments: candidate.comments || ''
      });

      // Populate interview levels from candidate data
      const levels = {};

      // Always include L1 with data from legacy fields or interview_levels
      levels.L1 = {
        level_name: 'L1',
        level_order: 1,
        status: candidate.l1_status || '',
        schedule_date: candidate.l1_schedule_date || '',
        schedule_time: candidate.l1_schedule_time || '',
        panel_name: candidate.l1_panel_name || '',
        panel_comment: candidate.l1_panel_comment || ''
      };

      // Add any additional levels from interview_levels array
      if (candidate.interview_levels && Array.isArray(candidate.interview_levels)) {
        candidate.interview_levels.forEach(level => {
          levels[level.level_name] = {
            level_name: level.level_name,
            level_order: level.level_order,
            status: level.status || '',
            schedule_date: level.schedule_date || '',
            schedule_time: level.schedule_time || '',
            panel_name: level.panel_name || '',
            panel_comment: level.panel_comment || ''
          };
        });
      }

      setInterviewLevels(levels);
      setError('');
      loadCustomOptions();
    }
  }, [isOpen, candidate, loadCustomOptions]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCandidateData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleInterviewLevelChange = (level, fieldName, value) => {
    setInterviewLevels(prev => ({
      ...prev,
      [level]: {
        ...prev[level],
        [fieldName]: value
      }
    }));
  };

  const addNewLevel = () => {
    const levelNumbers = Object.keys(interviewLevels).map(level => parseInt(level.substring(1)));
    const nextLevelNumber = Math.max(...levelNumbers) + 1;
    const newLevelName = `L${nextLevelNumber}`;

    setInterviewLevels(prev => ({
      ...prev,
      [newLevelName]: {
        level_name: newLevelName,
        level_order: nextLevelNumber,
        status: '',
        schedule_date: '',
        schedule_time: '',
        panel_name: '',
        panel_comment: ''
      }
    }));
  };

  const deleteLevel = (levelName) => {
    if (levelName === 'L1') return; // Don't allow deleting L1

    setInterviewLevels(prev => {
      const newLevels = { ...prev };
      delete newLevels[levelName];
      return newLevels;
    });
  };

  const handleFileChange = (file) => {
    setCandidateData(prev => ({
      ...prev,
      resume: file
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const token = localStorage.getItem('token');

      // Create FormData object for file upload
      const formData = new FormData();

      // Debug: Log the candidate data before submission
      console.log("Submitting candidate data for update:", candidateData);

      // Add all data to FormData, excluding empty resume if no new file was selected
      Object.keys(candidateData).forEach(key => {
        if (key === 'resume') {
          if (candidateData[key]) {
            formData.append(key, candidateData[key]);
          }
        } else if (candidateData[key] !== null && candidateData[key] !== undefined) {
          formData.append(key, candidateData[key]);
          // Debug: Log each field being added to FormData
          console.log(`Adding to FormData: ${key} = ${candidateData[key]}`);
        }
      });

      // Add interview levels data
      const levelsArray = Object.values(interviewLevels);
      formData.append('interview_levels', JSON.stringify(levelsArray));

      // Make API request
      const response = await axios.patch(`${API_URL}/api/candidates/${candidate.id}/`, formData, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      // Update the candidate
      if (onCandidateUpdated) {
        onCandidateUpdated(response.data);
      }

      // Close modal
      onClose();

    } catch (error) {
      console.error('Error updating candidate:', error);
      console.error('Error response:', error.response?.data);

      // Show more detailed error message
      if (error.response?.data) {
        // If there's a specific error message from the server
        const errorMessages = [];
        const errorData = error.response.data;

        // Handle different error formats
        if (typeof errorData === 'string') {
          errorMessages.push(errorData);
        } else if (typeof errorData === 'object') {
          // Loop through all error fields
          Object.keys(errorData).forEach(key => {
            const value = errorData[key];
            if (Array.isArray(value)) {
              errorMessages.push(`${key}: ${value.join(', ')}`);
            } else {
              errorMessages.push(`${key}: ${value}`);
            }
          });
        }

        if (errorMessages.length > 0) {
          setError(`Failed to update candidate: ${errorMessages.join('; ')}`);
        } else {
          setError('Failed to update candidate. Please try again.');
        }
      } else {
        setError('Failed to update candidate. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg w-full max-w-lg mx-auto overflow-y-auto max-h-[90vh]`}>
        <h2 className="text-xl font-semibold mb-4">Edit Candidate</h2>

        {error && (
          <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'} p-3 rounded-md mb-4`}>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Candidate Name */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Candidate Name
              </label>
              <input
                type="text"
                name="name"
                value={candidateData.name}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Full name of candidate"
                required
              />
            </div>

            {/* Candidate ID */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Candidate ID
              </label>
              <input
                type="text"
                name="candidate_id"
                value={candidateData.candidate_id}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Unique candidate identifier"
              />
            </div>

            {/* Primary Email */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Primary Email
              </label>
              <input
                type="email"
                name="primary_email"
                value={candidateData.primary_email}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Primary email address"
              />
            </div>

            {/* Mobile */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Mobile
              </label>
              <input
                type="tel"
                name="mobile"
                value={candidateData.mobile}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Mobile phone number"
              />
            </div>

            {/* SPOC */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                SPOC
              </label>
              <input
                type="text"
                name="spoc"
                value={candidateData.spoc}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Single Point of Contact"
              />
            </div>

            {/* Preferred Role */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Preferred Role
              </label>
              <select
                name="preferred_role"
                value={candidateData.preferred_role}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                <option value="">Select a role</option>
                {roleOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Optional Roles */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Optional Roles
              </label>
              <select
                name="optional_roles"
                value={candidateData.optional_roles}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select a role</option>
                {roleOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Total Experience */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Total Experience (in years)
              </label>
              <input
                type="number"
                name="total_experience"
                value={candidateData.total_experience}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="e.g. 5.5"
                step="0.1"
                min="0"
              />
            </div>

            {/* Last Job Date */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Last Job Date
              </label>
              <input
                type="date"
                name="last_job_date"
                value={candidateData.last_job_date}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            </div>

            {/* Resume */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Resume
              </label>
              {candidate.resume_filename && (
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'} mb-2`}>
                  Current file: {candidate.resume_filename}
                </p>
              )}
              <FileUpload
                onFileChange={handleFileChange}
                label="Upload Resume (PDF, DOC, DOCX)"
                acceptTypes=".pdf,.doc,.docx"
                darkMode={darkMode}
              />
              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                Leave empty to keep the existing resume
              </p>
            </div>

            {/* Dynamic Interview Levels */}
            {Object.keys(interviewLevels).sort().map(levelName => (
              <InterviewLevel
                key={levelName}
                level={levelName}
                levelData={interviewLevels[levelName]}
                onChange={handleInterviewLevelChange}
                onDelete={deleteLevel}
                canDelete={levelName !== 'L1'}
                darkMode={darkMode}
              />
            ))}

            {/* Add New Level Button */}
            <div className="flex justify-center">
              <button
                type="button"
                onClick={addNewLevel}
                className={`px-4 py-2 ${darkMode ? 'bg-green-600 hover:bg-green-700' : 'bg-green-500 hover:bg-green-600'} text-white rounded-md flex items-center space-x-2`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>Add New Level</span>
              </button>
            </div>

            {/* Jira Tickets */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Jira Ticket Numbers
              </label>
              <input
                type="text"
                name="jira_tickets"
                value={candidateData.jira_tickets}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Enter Jira ticket numbers separated by commas (e.g., JIRA-123, JIRA-456)"
              />
              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                Optional: Add multiple ticket numbers separated by commas
              </p>
            </div>

            {/* Comments */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Comments
              </label>
              <textarea
                name="comments"
                value={candidateData.comments}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Add any notes or feedback about this candidate"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Updating...' : 'Update Candidate'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditCandidateModal;
