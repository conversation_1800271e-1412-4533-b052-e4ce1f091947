# Generated by Django 5.2 on 2025-04-14 20:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('candidates', '0006_candidateranking_matching_skills_and_more'),
        ('jobs', '0004_alter_job_title'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Opportunity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of the opportunity', max_length=255)),
                ('hiring_status', models.CharField(choices=[('no_engagement', 'No Engagement'), ('l1_scheduled', 'L1 Scheduled'), ('l1_attended', 'L1 Attended'), ('l1_dropoff', 'L1 Dropoff'), ('l1_hold', 'L1 Hold'), ('l1_rejected', 'L1 Rejected'), ('l1_selected', 'L1 Selected'), ('l2_scheduled', 'L2 Scheduled'), ('l2_attended', 'L2 Attended'), ('l2_dropoff', 'L2 Dropoff'), ('l2_hold', 'L2 Hold'), ('l2_rejected', 'L2 Rejected'), ('l2_selected', 'L2 Selected'), ('l3_scheduled', 'L3 Scheduled'), ('l3_attended', 'L3 Attended'), ('l3_dropoff', 'L3 Dropoff'), ('l3_hold', 'L3 Hold'), ('l3_rejected', 'L3 Rejected'), ('l3_selected', 'L3 Selected')], default='no_engagement', max_length=50)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='opportunities', to='candidates.candidate')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_opportunities', to=settings.AUTH_USER_MODEL)),
                ('job', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='candidate_opportunities', to='jobs.job')),
            ],
            options={
                'verbose_name_plural': 'Opportunities',
                'ordering': ['-updated_at'],
            },
        ),
    ]
