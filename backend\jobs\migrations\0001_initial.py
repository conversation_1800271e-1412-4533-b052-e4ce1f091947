# Generated by Django 5.2 on 2025-04-13 12:17

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Job',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, max_length=200, null=True)),
                ('description_document', models.FileField(blank=True, null=True, upload_to='job_descriptions/')),
                ('positions_available', models.PositiveIntegerField(blank=True, null=True)),
                ('hiring_team', models.CharField(blank=True, max_length=200, null=True)),
                ('hiring_manager', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('ta_incharge', models.CharField(blank=True, max_length=100, null=True)),
                ('created_on', models.DateField(blank=True, default=django.utils.timezone.now, null=True)),
                ('recruitment_status', models.CharField(choices=[('active', 'Actively Recruiting'), ('hold', 'On Hold'), ('stopped', 'Stopped')], default='active', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_jobs', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
