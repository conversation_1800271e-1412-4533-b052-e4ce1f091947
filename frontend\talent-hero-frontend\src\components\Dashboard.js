import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../config';
import JobCard from './JobCard';
import AddJobModal from './AddJobModal';
import EditJobModal from './EditJobModal';
import CandidateCard from './CandidateCard';
import AddCandidateModal from './AddCandidateModal';
import EditCandidateModal from './EditCandidateModal';
import BulkUploadModal from './BulkUploadModal';
import SavedFiltersView from './SavedFiltersView';
import CustomizeMenu from './CustomizeMenu';
import DebugModal from './DebugModal';

const Dashboard = () => {
  const { currentUser, logout } = useAuth();
  const { darkMode, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('dashboard');

  // Debug active section changes
  useEffect(() => {
    console.log('Active section changed to:', activeSection);
  }, [activeSection]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Jobs state
  const [jobs, setJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);
  
  // Candidates state
  const [candidates, setCandidates] = useState([]);
  const [isLoadingCandidates, setIsLoadingCandidates] = useState(false);
  const [candidateError, setCandidateError] = useState('');
  const [isAddCandidateModalOpen, setIsAddCandidateModalOpen] = useState(false);
  const [isEditCandidateModalOpen, setIsEditCandidateModalOpen] = useState(false);
  const [isBulkUploadModalOpen, setIsBulkUploadModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  
  // Filters state
  const [showSavedFilters, setShowSavedFilters] = useState(false);
  const [hasSavedFilters, setHasSavedFilters] = useState(false);
  const [jobSearchTerm, setJobSearchTerm] = useState('');
  const [candidateSearchTerm, setCandidateSearchTerm] = useState('');

  // Bulk selection states
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectAllJobs, setSelectAllJobs] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState([]);
  const [selectAllCandidates, setSelectAllCandidates] = useState(false);
  
  // Customize menu state
  const [showCustomizeMenu, setShowCustomizeMenu] = useState(false);
  const [customOptions, setCustomOptions] = useState({
    jobTitles: []
  });

  // Debug modal state
  const [showDebugModal, setShowDebugModal] = useState(false);
  const [debugInfo, setDebugInfo] = useState({
    isLoading: false,
    error: null,
    data: [],
    lastFetchTime: null
  });

  // Fetch jobs when the jobs section is selected
  useEffect(() => {
    if (activeSection === 'jobs') {
      fetchJobs();
      checkForSavedFilters();
    }
  }, [activeSection]);
  
  // Fetch candidates when the candidates section is selected
  useEffect(() => {
    if (activeSection === 'candidates') {
      fetchCandidates();
    }
  }, [activeSection]);
  
  // Load custom options
  useEffect(() => {
    loadCustomOptions();
  }, []);
  
  // Additional useEffect to log whenever candidates state changes
  useEffect(() => {
    console.log('Candidates state updated:', candidates);
    console.log('Candidates count:', Array.isArray(candidates) ? candidates.length : 'not an array');
  }, [candidates]);

  // Make sure to call fetchCandidates when the app first loads
  useEffect(() => {
    if (currentUser) {
      fetchJobs();
      fetchCandidates(); // Load candidates on initial load
      checkForSavedFilters(); // Also check for saved filters on initial load
    }
  }, [currentUser]);

  // Auto-clear error and success messages
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  const loadCustomOptions = () => {
    const jobTitles = JSON.parse(localStorage.getItem('customJobTitles') || '[]');

    setCustomOptions({ jobTitles });
  };
  
  const checkForSavedFilters = () => {
    const filters = JSON.parse(localStorage.getItem('savedCandidateFilters') || '[]');
    console.log('Checking for saved filters:', filters);
    setHasSavedFilters(filters.length > 0);
  };

  const fetchJobs = async () => {
    setIsLoading(true);
    setError('');
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('Authentication token is missing');
      }

      console.log('Fetching jobs with token:', token ? 'Token exists' : 'No token');

      const response = await axios.get(`${API_URL}/api/jobs/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Jobs API response:', response.data);

      // Ensure we always set an array, even if API returns something unexpected
      if (Array.isArray(response.data)) {
        setJobs(response.data);
      } else if (response.data && Array.isArray(response.data.results)) {
        // Handle case where API returns paginated results
        setJobs(response.data.results);
      } else {
        console.error('Unexpected API response format:', response.data);
        setJobs([]);
        setError('Received unexpected data format from server');
      }
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setError('Failed to load jobs. Please try again.');

      // Provide more detailed error information
      if (error.response) {
        console.error('Response error data:', error.response.data);
        console.error('Response error status:', error.response.status);
      }

      // Set empty array on error
      setJobs([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  const fetchCandidates = async () => {
    setIsLoadingCandidates(true);
    setCandidateError('');

    // Update debug info
    setDebugInfo(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('Authentication token is missing');
      }

      console.log('Fetching candidates with token:', token ? 'Token exists' : 'No token');

      const response = await axios.get(`${API_URL}/api/candidates/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Candidates API response:', response.data);

      let candidatesData = [];

      // Ensure we always set an array, even if API returns something unexpected
      if (Array.isArray(response.data)) {
        candidatesData = response.data;
        setCandidates(response.data);
      } else if (response.data && Array.isArray(response.data.results)) {
        // Handle case where API returns paginated results
        candidatesData = response.data.results;
        setCandidates(response.data.results);
      } else {
        console.error('Unexpected API response format:', response.data);
        setCandidates([]);
        setCandidateError('Received unexpected data format from server');
      }

      // Update debug info with successful data
      setDebugInfo(prev => ({
        ...prev,
        isLoading: false,
        error: null,
        data: candidatesData,
        lastFetchTime: new Date().toLocaleString()
      }));

    } catch (error) {
      console.error('Error fetching candidates:', error);
      const errorMessage = 'Failed to load candidates. Please try again.';
      setCandidateError(errorMessage);

      // Provide more detailed error information
      if (error.response) {
        console.error('Response error data:', error.response.data);
        console.error('Response error status:', error.response.status);
      }

      // Set empty array on error
      setCandidates([]);

      // Update debug info with error
      setDebugInfo(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
        data: [],
        lastFetchTime: new Date().toLocaleString()
      }));

    } finally {
      setIsLoadingCandidates(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleAddJob = (newJob) => {
    setJobs(prevJobs => [newJob, ...prevJobs]);
  };

  const handleEditJob = (updatedJob) => {
    setJobs(prevJobs => 
      prevJobs.map(job => job.id === updatedJob.id ? updatedJob : job)
    );
  };

  const handleDeleteJob = async (jobId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/jobs/${jobId}/`, {
        headers: {
          'Authorization': `Token ${token}`
        }
      });
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId));
    } catch (error) {
      console.error('Error deleting job:', error);
      setError('Failed to delete job. Please try again.');
    }
  };
  
  const handleAddCandidate = (newCandidate) => {
    setCandidates(prevCandidates => [newCandidate, ...prevCandidates]);
  };

  const handleBulkUploadCandidates = (newCandidates) => {
    setCandidates(prevCandidates => [...newCandidates, ...prevCandidates]);
    // Refresh the candidates list to get the latest data
    fetchCandidates();
  };

  const handleEditCandidate = (updatedCandidate) => {
    setCandidates(prevCandidates => 
      prevCandidates.map(candidate => candidate.id === updatedCandidate.id ? updatedCandidate : candidate)
    );
  };

  const handleDeleteCandidate = async (candidateId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/candidates/${candidateId}/`, {
        headers: {
          'Authorization': `Token ${token}`
        }
      });
      setCandidates(prevCandidates => prevCandidates.filter(candidate => candidate.id !== candidateId));
    } catch (error) {
      console.error('Error deleting candidate:', error);
      setCandidateError('Failed to delete candidate. Please try again.');
    }
  };
  
  const handleSaveFilter = (filterData) => {
    checkForSavedFilters();
  };

  // Filter jobs based on search term
  const filteredJobs = jobs.filter(job => {
    if (!jobSearchTerm) return true;
    const searchLower = jobSearchTerm.toLowerCase();
    return (
      (job.title_display && job.title_display.toLowerCase().includes(searchLower)) ||
      (job.hiring_manager && job.hiring_manager.toLowerCase().includes(searchLower)) ||
      (job.hiring_team && job.hiring_team.toLowerCase().includes(searchLower)) ||
      (job.ta_incharge && job.ta_incharge.toLowerCase().includes(searchLower))
    );
  });

  // Filter candidates based on search term
  const filteredCandidates = candidates.filter(candidate => {
    if (!candidateSearchTerm) return true;
    const searchLower = candidateSearchTerm.toLowerCase();
    return (
      (candidate.name && candidate.name.toLowerCase().includes(searchLower)) ||
      (candidate.email && candidate.email.toLowerCase().includes(searchLower)) ||
      (candidate.preferred_role_display && candidate.preferred_role_display.toLowerCase().includes(searchLower)) ||
      (candidate.hiring_status_display && candidate.hiring_status_display.toLowerCase().includes(searchLower))
    );
  });

  // Bulk operations for jobs
  const handleJobSelection = (jobId) => {
    setSelectedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleSelectAllJobs = () => {
    if (selectAllJobs) {
      setSelectedJobs([]);
    } else {
      setSelectedJobs(filteredJobs.map(job => job.id));
    }
    setSelectAllJobs(!selectAllJobs);
  };

  const handleBulkDeleteJobs = async () => {
    if (!window.confirm(`Are you sure you want to delete ${selectedJobs.length} job(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/jobs/bulk-delete/`, {
        data: { job_ids: selectedJobs },
        headers: { Authorization: `Token ${token}` }
      });

      setJobs(prevJobs => prevJobs.filter(job => !selectedJobs.includes(job.id)));
      setSelectedJobs([]);
      setSelectAllJobs(false);
      setSuccess(`${selectedJobs.length} job(s) deleted successfully`);
    } catch (err) {
      setError('Failed to delete jobs');
    }
  };

  // Bulk operations for candidates
  const handleCandidateSelection = (candidateId) => {
    setSelectedCandidates(prev =>
      prev.includes(candidateId)
        ? prev.filter(id => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  const handleSelectAllCandidates = () => {
    if (selectAllCandidates) {
      setSelectedCandidates([]);
    } else {
      setSelectedCandidates(filteredCandidates.map(candidate => candidate.id));
    }
    setSelectAllCandidates(!selectAllCandidates);
  };

  const handleBulkDeleteCandidates = async () => {
    if (!window.confirm(`Are you sure you want to delete ${selectedCandidates.length} candidate(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/candidates/bulk-delete/`, {
        data: { candidate_ids: selectedCandidates },
        headers: { Authorization: `Token ${token}` }
      });

      setCandidates(prevCandidates => prevCandidates.filter(candidate => !selectedCandidates.includes(candidate.id)));
      setSelectedCandidates([]);
      setSelectAllCandidates(false);
      setSuccess(`${selectedCandidates.length} candidate(s) deleted successfully`);
    } catch (err) {
      setError('Failed to delete candidates');
    }
  };

  const handleCustomizeUpdate = (newOptions) => {
    setCustomOptions(newOptions);
    // This will trigger the child components to re-fetch from localStorage
  };
  
  return (
    <div className={`min-h-screen flex flex-col ${darkMode ? 'dark bg-gray-900 text-gray-100' : 'bg-gray-100 text-gray-800'}`}>
      {/* Sidebar */}
      <div 
        className={`${sidebarCollapsed ? 'w-24' : 'w-72'} transition-all duration-300 ease-in-out 
        ${darkMode ? 'bg-gray-800 text-gray-100' : 'bg-gray-800 text-white'}
        min-h-screen fixed overflow-y-auto overflow-x-hidden`}
      >
        {/* Collapse/Expand Button - Now positioned further left */}
        <button 
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="absolute -right-0.5 top-1/2 transform -translate-y-1/2 bg-gray-800 rounded-full p-1 text-gray-400 hover:text-white border border-gray-700 z-10"
        >
          {sidebarCollapsed ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          )}
        </button>
        
        <div className="p-4 flex flex-col h-full">
          <div className="flex items-center justify-between mb-8 relative h-10">
            <div className={`absolute left-0 transition-all duration-300 ease-in-out ${sidebarCollapsed ? 'opacity-0 -translate-x-10' : 'opacity-100 translate-x-0'}`}>
              <h1 className="text-xl font-bold truncate">Talent Hero</h1>
              <p className="text-xs text-gray-400 truncate">AI-Powered Talent Acquisition System</p>
            </div>
            <div className={`absolute left-0 right-0 mx-auto w-10 text-center transition-all duration-300 ease-in-out ${sidebarCollapsed ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>
              <span className="text-2xl font-bold">TH</span>
            </div>
          </div>
          
          <nav className="flex-1">
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => setActiveSection('dashboard')}
                  className={`flex items-center w-full p-2 rounded-md ${activeSection === 'dashboard' ? 'bg-blue-700' : 'hover:bg-gray-700'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Dashboard</span>
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveSection('jobs')}
                  className={`flex items-center w-full p-2 rounded-md ${activeSection === 'jobs' ? 'bg-blue-700' : 'hover:bg-gray-700'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Jobs</span>
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveSection('candidates')}
                  className={`flex items-center w-full p-2 rounded-md ${activeSection === 'candidates' ? 'bg-blue-700' : 'hover:bg-gray-700'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Candidates</span>
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveSection('status')}
                  className={`flex items-center w-full p-2 rounded-md ${activeSection === 'status' ? 'bg-blue-700' : 'hover:bg-gray-700'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Status</span>
                </button>
              </li>
              {currentUser && currentUser.is_admin && (
                <li>
                  <button
                    onClick={() => navigate('/admin')}
                    className={`flex items-center w-full p-2 rounded-md hover:bg-gray-700`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Admin Panel</span>
                  </button>
                </li>
              )}
            </ul>
          </nav>
          
          <div>
            <button
              onClick={() => setShowCustomizeMenu(true)}
              className={`flex items-center w-full p-2 rounded-md hover:bg-gray-700`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Customize Menu</span>
            </button>
            <button
              onClick={toggleDarkMode}
              className={`flex items-center w-full p-2 rounded-md hover:bg-gray-700 mt-2`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {darkMode ? (
                  // Sun icon for light mode
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                ) : (
                  // Moon icon for dark mode
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                )}
              </svg>
              <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>
                {darkMode ? 'Light Mode' : 'Dark Mode'}
              </span>
            </button>
            <button
              onClick={handleLogout}
              className={`flex items-center w-full p-2 rounded-md hover:bg-gray-700 mt-2`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 min-w-8 mr-2 transition-all duration-300 ease-in-out" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v6" />
              </svg>
              <span className={`transition-all duration-300 ease-in-out truncate ${sidebarCollapsed ? 'opacity-0 absolute' : 'opacity-100 relative'}`}>Logout</span>
            </button>
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <main 
        className={`flex-1 ml-20 xl:ml-64 transition-all duration-300 ease-in-out p-6 
        ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}
        style={{ marginLeft: sidebarCollapsed ? '6rem' : '18rem' }}
      >
        {/* Dashboard Content */}
        {activeSection === 'dashboard' && (
          <div className="space-y-6">
            {/* Metrics Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-blue-900' : 'bg-blue-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-blue-300' : 'text-blue-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Total Jobs</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{jobs.length}</h3>
                  </div>
                </div>
              </div>
              
              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-green-900' : 'bg-green-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-green-300' : 'text-green-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Total Opportunities</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{candidates.length}</h3>
                  </div>
                </div>
              </div>
              
              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-purple-900' : 'bg-purple-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-purple-300' : 'text-purple-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Active Jobs</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                      {jobs.filter(job => job.status === 'active').length}
                    </h3>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Welcome Section */}
            <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h2 className="text-lg font-semibold mb-4">Welcome, {currentUser.username}!</h2>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                This is your talent management dashboard. Use the sidebar to navigate to different sections of the application.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className={`${darkMode ? 'bg-blue-900 border-blue-800 text-blue-100' : 'bg-blue-50 border-blue-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-blue-200' : 'text-blue-800'}`}>Jobs</h3>
                  <p className={`${darkMode ? 'text-blue-300' : 'text-blue-600'} mt-1`}>Manage job listings and descriptions</p>
                  <button 
                    onClick={() => setActiveSection('jobs')}
                    className={`mt-4 ${darkMode ? 'text-blue-300 hover:text-blue-200' : 'text-blue-600 hover:text-blue-800'} text-sm font-medium`}
                  >
                    View Jobs →
                  </button>
                </div>
                <div className={`${darkMode ? 'bg-green-900 border-green-800 text-green-100' : 'bg-green-50 border-green-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-green-200' : 'text-green-800'}`}>Candidates</h3>
                  <p className={`${darkMode ? 'text-green-300' : 'text-green-600'} mt-1`}>Track candidate profiles and applications</p>
                  <button 
                    onClick={() => setActiveSection('candidates')}
                    className={`mt-4 ${darkMode ? 'text-green-300 hover:text-green-200' : 'text-green-600 hover:text-green-800'} text-sm font-medium`}
                  >
                    View Candidates →
                  </button>
                </div>
                <div className={`${darkMode ? 'bg-purple-900 border-purple-800 text-purple-100' : 'bg-purple-50 border-purple-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-purple-200' : 'text-purple-800'}`}>Status</h3>
                  <p className={`${darkMode ? 'text-purple-300' : 'text-purple-600'} mt-1`}>Monitor recruitment progress</p>
                  <button 
                    onClick={() => setActiveSection('status')}
                    className={`mt-4 ${darkMode ? 'text-purple-300 hover:text-purple-200' : 'text-purple-600 hover:text-purple-800'} text-sm font-medium`}
                  >
                    View Status →
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Jobs Content */}
        {activeSection === 'jobs' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Job Listings</h2>
              <div className="flex space-x-2">
                {/* Bulk Actions for Jobs */}
                {selectedJobs.length > 0 && (
                  <button
                    onClick={handleBulkDeleteJobs}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    Delete Selected ({selectedJobs.length})
                  </button>
                )}
                <button
                  onClick={() => {
                    console.log('Manual refresh jobs clicked');
                    fetchJobs();
                  }}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Refresh Jobs
                </button>
                {/* Show button regardless of hasSavedFilters status for debugging */}
                <button
                  onClick={() => setShowSavedFilters(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  View Saved Filters
                </button>
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create New Job
                </button>
              </div>
            </div>

            {/* Jobs Search Bar */}
            <div className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search jobs by title, manager, team, or TA..."
                  value={jobSearchTerm}
                  onChange={(e) => setJobSearchTerm(e.target.value)}
                  className={`w-full px-4 py-2 pl-10 pr-4 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                {jobSearchTerm && (
                  <button
                    onClick={() => setJobSearchTerm('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-400 hover:text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              {jobSearchTerm && (
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Showing {filteredJobs.length} of {jobs.length} jobs
                </p>
              )}
            </div>

            {/* Jobs Bulk Selection */}
            {filteredJobs.length > 0 && (
              <div className="mb-4 flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectAllJobs}
                    onChange={handleSelectAllJobs}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Select All Jobs ({filteredJobs.length})
                  </span>
                </label>
                {selectedJobs.length > 0 && (
                  <span className={`ml-4 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                    {selectedJobs.length} selected
                  </span>
                )}
              </div>
            )}

            {error && (
              <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-100 text-green-700 p-3 rounded-md mb-4">
                {success}
              </div>
            )}

            {isLoading ? (
              <div className="text-center py-10">
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading jobs...</p>
              </div>
            ) : filteredJobs.length === 0 ? (
              <div className={`text-center py-10 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                  {jobSearchTerm ? `No jobs found matching "${jobSearchTerm}"` : 'No jobs found. Click "Create New Job" to post your first job opening.'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredJobs.map(job => (
                  <JobCard
                    key={job.id}
                    job={job}
                    onEdit={(job) => {
                      setSelectedJob(job);
                      // Only open the modal after selectedJob has been set
                      setTimeout(() => {
                        setIsEditModalOpen(true);
                      }, 0);
                    }}
                    onDelete={handleDeleteJob}
                    onSaveFilter={handleSaveFilter}
                    darkMode={darkMode}
                    isSelected={selectedJobs.includes(job.id)}
                    onSelect={handleJobSelection}
                  />
                ))}
              </div>
            )}

            {/* Add Job Modal */}
            <AddJobModal 
              isOpen={isAddModalOpen}
              onClose={() => setIsAddModalOpen(false)}
              onJobAdded={handleAddJob}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />

            {/* Edit Job Modal */}
            <EditJobModal 
              isOpen={isEditModalOpen}
              onClose={() => {
                setIsEditModalOpen(false);
                setSelectedJob(null);
              }}
              onJobUpdated={handleEditJob}
              job={selectedJob}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />
            
            {/* Saved Filters View */}
            {showSavedFilters && (
              <SavedFiltersView
                onClose={() => setShowSavedFilters(false)}
                onFilterSelect={handleSaveFilter}
                jobs={jobs}
                darkMode={darkMode}
              />
            )}
          </div>
        )}
        
        {/* Candidates Content */}
        {activeSection === 'candidates' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Candidate Profiles</h2>
              <div className="flex space-x-2">
                {/* Bulk Actions for Candidates */}
                {selectedCandidates.length > 0 && (
                  <button
                    onClick={handleBulkDeleteCandidates}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    Delete Selected ({selectedCandidates.length})
                  </button>
                )}
                <button
                  onClick={() => setShowDebugModal(true)}
                  className={`p-2 ${darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'} rounded-md`}
                  title="Debug Information"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                </button>
                <button
                  onClick={() => {
                    console.log('Manual refresh candidates clicked');
                    fetchCandidates();
                  }}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Refresh Candidates
                </button>
                <button
                  onClick={() => setIsBulkUploadModalOpen(true)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                >
                  Bulk Upload
                </button>
                <button
                  onClick={() => setIsAddCandidateModalOpen(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Add New Candidate
                </button>
              </div>
            </div>

            {/* Candidates Search Bar */}
            <div className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search candidates by name, email, role, or status..."
                  value={candidateSearchTerm}
                  onChange={(e) => setCandidateSearchTerm(e.target.value)}
                  className={`w-full px-4 py-2 pl-10 pr-4 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                {candidateSearchTerm && (
                  <button
                    onClick={() => setCandidateSearchTerm('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-400 hover:text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              {candidateSearchTerm && (
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Showing {filteredCandidates.length} of {candidates.length} candidates
                </p>
              )}
            </div>

            {/* Candidates Bulk Selection */}
            {filteredCandidates.length > 0 && (
              <div className="mb-4 flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectAllCandidates}
                    onChange={handleSelectAllCandidates}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Select All Candidates ({filteredCandidates.length})
                  </span>
                </label>
                {selectedCandidates.length > 0 && (
                  <span className={`ml-4 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                    {selectedCandidates.length} selected
                  </span>
                )}
              </div>
            )}

            {candidateError && (
              <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
                {candidateError}
              </div>
            )}

            {isLoadingCandidates ? (
              <div className="text-center py-10">
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading candidates...</p>
              </div>
            ) : !Array.isArray(filteredCandidates) || filteredCandidates.length === 0 ? (
              <div className={`text-center py-10 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                  {candidateSearchTerm ? `No candidates found matching "${candidateSearchTerm}"` : 'No candidates found. Click "Add New Candidate" to create your first candidate profile.'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredCandidates.map(candidate => (
                  <CandidateCard
                    key={candidate.id}
                    candidate={candidate}
                    onEdit={(candidate) => {
                      setSelectedCandidate(candidate);
                      setIsEditCandidateModalOpen(true);
                    }}
                    onDelete={handleDeleteCandidate}
                    darkMode={darkMode}
                    isSelected={selectedCandidates.includes(candidate.id)}
                    onSelect={handleCandidateSelection}
                  />
                ))}
              </div>
            )}

            {/* Add Candidate Modal */}
            <AddCandidateModal
              isOpen={isAddCandidateModalOpen}
              onClose={() => setIsAddCandidateModalOpen(false)}
              onCandidateAdded={handleAddCandidate}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />

            {/* Edit Candidate Modal */}
            <EditCandidateModal
              isOpen={isEditCandidateModalOpen}
              onClose={() => {
                setIsEditCandidateModalOpen(false);
                setSelectedCandidate(null);
              }}
              onCandidateUpdated={handleEditCandidate}
              candidate={selectedCandidate}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />

            {/* Bulk Upload Modal */}
            <BulkUploadModal
              isOpen={isBulkUploadModalOpen}
              onClose={() => setIsBulkUploadModalOpen(false)}
              onCandidatesAdded={handleBulkUploadCandidates}
              darkMode={darkMode}
            />
          </div>
        )}
        
        {/* Status Content */}
        {activeSection === 'status' && (
          <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
            <h2 className="text-lg font-semibold mb-4">Recruitment Status</h2>
            <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              This feature is coming soon. Here you will be able to track the status of all recruitment activities.
            </p>
          </div>
        )}
      </main>

      {/* Global Modals - These will be available regardless of which section is active */}
      {showCustomizeMenu && (
        <CustomizeMenu
          isOpen={showCustomizeMenu}
          onClose={() => setShowCustomizeMenu(false)}
          onUpdate={handleCustomizeUpdate}
          darkMode={darkMode}
        />
      )}

      {/* Debug Modal */}
      <DebugModal
        isOpen={showDebugModal}
        onClose={() => setShowDebugModal(false)}
        debugInfo={debugInfo}
        darkMode={darkMode}
      />
    </div>
  );
};

export default Dashboard;
