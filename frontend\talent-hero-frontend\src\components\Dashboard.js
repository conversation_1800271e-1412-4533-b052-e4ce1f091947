import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../config';
import JobCard from './JobCard';
import AddJobModal from './AddJobModal';
import EditJobModal from './EditJobModal';
import CandidateCard from './CandidateCard';
import AddCandidateModal from './AddCandidateModal';
import EditCandidateModal from './EditCandidateModal';
import BulkUploadModal from './BulkUploadModal';
import SavedFiltersView from './SavedFiltersView';
import CustomizeMenu from './CustomizeMenu';
import DebugModal from './DebugModal';

const Dashboard = () => {
  const { currentUser, logout } = useAuth();
  const { darkMode, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  
  // Active section state
  const [activeSection, setActiveSection] = useState('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Jobs state
  const [jobs, setJobs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);
  
  // Candidates state
  const [candidates, setCandidates] = useState([]);
  const [isLoadingCandidates, setIsLoadingCandidates] = useState(false);
  const [candidateError, setCandidateError] = useState('');
  const [isAddCandidateModalOpen, setIsAddCandidateModalOpen] = useState(false);
  const [isEditCandidateModalOpen, setIsEditCandidateModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [isBulkUploadModalOpen, setIsBulkUploadModalOpen] = useState(false);
  
  // Filters state
  const [showSavedFilters, setShowSavedFilters] = useState(false);
  const [hasSavedFilters, setHasSavedFilters] = useState(false);
  const [jobSearchTerm, setJobSearchTerm] = useState('');
  const [candidateSearchTerm, setCandidateSearchTerm] = useState('');
  
  // Bulk selection states
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [selectAllJobs, setSelectAllJobs] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState([]);
  const [selectAllCandidates, setSelectAllCandidates] = useState(false);
  
  // Customize menu state
  const [showCustomizeMenu, setShowCustomizeMenu] = useState(false);
  const [customOptions, setCustomOptions] = useState({
    jobTitles: []
  });

  // Debug modal state
  const [showDebugModal, setShowDebugModal] = useState(false);
  const [debugInfo, setDebugInfo] = useState({
    candidatesCount: 0,
    candidatesArray: [],
    lastFetchTime: null
  });

  // Debug active section changes
  useEffect(() => {
    console.log('Active section changed to:', activeSection);
  }, [activeSection]);

  // Fetch jobs when the jobs section is selected
  useEffect(() => {
    if (activeSection === 'jobs') {
      fetchJobs();
      checkForSavedFilters();
    }
  }, [activeSection]);
  
  // Fetch candidates when the candidates section is selected
  useEffect(() => {
    if (activeSection === 'candidates') {
      fetchCandidates();
    }
  }, [activeSection]);

  // Debug candidates state
  useEffect(() => {
    console.log('Candidates state updated:', candidates);
    console.log('Candidates count:', Array.isArray(candidates) ? candidates.length : 'not an array');
  }, [candidates]);

  // Make sure to call fetchCandidates when the app first loads
  useEffect(() => {
    if (currentUser) {
      fetchJobs();
      fetchCandidates(); // Load candidates on initial load
      checkForSavedFilters(); // Also check for saved filters on initial load
    }
  }, [currentUser]);

  // Auto-clear error and success messages
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);
  
  const loadCustomOptions = () => {
    try {
      const savedOptions = localStorage.getItem('customOptions');
      if (savedOptions) {
        setCustomOptions(JSON.parse(savedOptions));
      }
    } catch (error) {
      console.error('Error loading custom options:', error);
    }
  };

  useEffect(() => {
    loadCustomOptions();
  }, []);

  const checkForSavedFilters = () => {
    try {
      const filters = JSON.parse(localStorage.getItem('savedFilters') || '[]');
      setHasSavedFilters(filters.length > 0);
    } catch (error) {
      console.error('Error checking saved filters:', error);
      setHasSavedFilters(false);
    }
  };

  const fetchJobs = async () => {
    setIsLoading(true);
    setError('');
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('Authentication token is missing');
      }

      const response = await axios.get(`${API_URL}/api/jobs/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        }
      });

      console.log('Jobs API response:', response.data);

      if (Array.isArray(response.data)) {
        setJobs(response.data);
      } else if (response.data && Array.isArray(response.data.results)) {
        // Handle case where API returns paginated results
        setJobs(response.data.results);
      } else {
        console.error('Unexpected API response format:', response.data);
        setJobs([]);
        setError('Received unexpected data format from server');
      }
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setError('Failed to load jobs. Please try again.');

      // Provide more detailed error information
      if (error.response) {
        console.error('Response error data:', error.response.data);
        console.error('Response error status:', error.response.status);
        
        if (error.response.status === 401) {
          setError('Authentication failed. Please log in again.');
          logout();
          navigate('/login');
        } else if (error.response.status === 403) {
          setError('You do not have permission to view jobs.');
        } else if (error.response.status >= 500) {
          setError('Server error. Please try again later.');
        }
      } else if (error.request) {
        console.error('Request error:', error.request);
        setError('Network error. Please check your connection.');
      } else {
        console.error('Error message:', error.message);
        setError(`Error: ${error.message}`);
      }
      
      setJobs([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCandidates = async () => {
    setIsLoadingCandidates(true);
    setCandidateError('');
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/candidates/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        }
      });

      console.log('Candidates API response:', response.data);
      
      if (Array.isArray(response.data)) {
        setCandidates(response.data);
        setDebugInfo(prev => ({
          ...prev,
          candidatesCount: response.data.length,
          candidatesArray: response.data,
          lastFetchTime: new Date().toISOString()
        }));
      } else if (response.data && Array.isArray(response.data.results)) {
        setCandidates(response.data.results);
        setDebugInfo(prev => ({
          ...prev,
          candidatesCount: response.data.results.length,
          candidatesArray: response.data.results,
          lastFetchTime: new Date().toISOString()
        }));
      } else {
        console.error('Unexpected candidates API response format:', response.data);
        setCandidates([]);
        setCandidateError('Received unexpected data format from server');
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      setCandidateError('Failed to load candidates. Please try again.');
      setCandidates([]);
      
      if (error.response?.status === 401) {
        logout();
        navigate('/login');
      }
    } finally {
      setIsLoadingCandidates(false);
    }
  };

  const handleAddJob = (newJob) => {
    setJobs(prevJobs => [newJob, ...prevJobs]);
    setSuccess('Job created successfully!');
  };

  const handleEditJob = (updatedJob) => {
    setJobs(prevJobs => prevJobs.map(job => 
      job.id === updatedJob.id ? updatedJob : job
    ));
    setSuccess('Job updated successfully!');
  };

  const handleDeleteJob = async (jobId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/jobs/${jobId}/`, {
        headers: { Authorization: `Token ${token}` }
      });
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId));
      setSuccess('Job deleted successfully!');
    } catch (error) {
      console.error('Error deleting job:', error);
      setError('Failed to delete job. Please try again.');
    }
  };
  
  const handleAddCandidate = (newCandidate) => {
    setCandidates(prevCandidates => [newCandidate, ...prevCandidates]);
    setSuccess('Candidate added successfully!');
  };

  const handleEditCandidate = (updatedCandidate) => {
    setCandidates(prevCandidates => prevCandidates.map(candidate => 
      candidate.id === updatedCandidate.id ? updatedCandidate : candidate
    ));
    setSuccess('Candidate updated successfully!');
  };

  const handleDeleteCandidate = async (candidateId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/candidates/${candidateId}/`, {
        headers: { Authorization: `Token ${token}` }
      });
      setCandidates(prevCandidates => prevCandidates.filter(candidate => candidate.id !== candidateId));
      setSuccess('Candidate deleted successfully!');
    } catch (error) {
      console.error('Error deleting candidate:', error);
      setCandidateError('Failed to delete candidate. Please try again.');
    }
  };

  // Filter jobs based on search term
  const filteredJobs = jobs.filter(job => {
    if (!jobSearchTerm) return true;
    const searchLower = jobSearchTerm.toLowerCase();
    return (
      (job.title_display && job.title_display.toLowerCase().includes(searchLower)) ||
      (job.hiring_manager && job.hiring_manager.toLowerCase().includes(searchLower)) ||
      (job.team && job.team.toLowerCase().includes(searchLower)) ||
      (job.ta_incharge && job.ta_incharge.toLowerCase().includes(searchLower))
    );
  });

  // Filter candidates based on search term
  const filteredCandidates = candidates.filter(candidate => {
    if (!candidateSearchTerm) return true;
    const searchLower = candidateSearchTerm.toLowerCase();
    return (
      (candidate.name && candidate.name.toLowerCase().includes(searchLower)) ||
      (candidate.email && candidate.email.toLowerCase().includes(searchLower)) ||
      (candidate.preferred_role_display && candidate.preferred_role_display.toLowerCase().includes(searchLower)) ||
      (candidate.hiring_status_display && candidate.hiring_status_display.toLowerCase().includes(searchLower))
    );
  });

  // Bulk operations for jobs
  const handleJobSelection = (jobId) => {
    setSelectedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleSelectAllJobs = () => {
    if (selectAllJobs) {
      setSelectedJobs([]);
    } else {
      setSelectedJobs(filteredJobs.map(job => job.id));
    }
    setSelectAllJobs(!selectAllJobs);
  };

  const handleBulkDeleteJobs = async () => {
    if (!window.confirm(`Are you sure you want to delete ${selectedJobs.length} job(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/jobs/bulk-delete/`, {
        data: { job_ids: selectedJobs },
        headers: { Authorization: `Token ${token}` }
      });

      setJobs(prevJobs => prevJobs.filter(job => !selectedJobs.includes(job.id)));
      setSelectedJobs([]);
      setSelectAllJobs(false);
      setSuccess(`${selectedJobs.length} job(s) deleted successfully`);
    } catch (err) {
      setError('Failed to delete jobs');
    }
  };

  // Bulk operations for candidates
  const handleCandidateSelection = (candidateId) => {
    setSelectedCandidates(prev =>
      prev.includes(candidateId)
        ? prev.filter(id => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  const handleSelectAllCandidates = () => {
    if (selectAllCandidates) {
      setSelectedCandidates([]);
    } else {
      setSelectedCandidates(filteredCandidates.map(candidate => candidate.id));
    }
    setSelectAllCandidates(!selectAllCandidates);
  };

  const handleBulkDeleteCandidates = async () => {
    if (!window.confirm(`Are you sure you want to delete ${selectedCandidates.length} candidate(s)? This action cannot be undone.`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/candidates/bulk-delete/`, {
        data: { candidate_ids: selectedCandidates },
        headers: { Authorization: `Token ${token}` }
      });

      setCandidates(prevCandidates => prevCandidates.filter(candidate => !selectedCandidates.includes(candidate.id)));
      setSelectedCandidates([]);
      setSelectAllCandidates(false);
      setSuccess(`${selectedCandidates.length} candidate(s) deleted successfully`);
    } catch (err) {
      setCandidateError('Failed to delete candidates');
    }
  };

  const handleCustomizeUpdate = (newOptions) => {
    setCustomOptions(newOptions);
    // This will trigger the child components to re-fetch from localStorage
  };

  const handleSaveFilter = (filterData) => {
    checkForSavedFilters();
  };

  // Render content based on active section
  const renderContent = () => {
    console.log('renderContent called with activeSection:', activeSection);
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="space-y-6">
            {/* Metrics Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-blue-900' : 'bg-blue-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-blue-300' : 'text-blue-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Total Jobs</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{jobs.length}</h3>
                  </div>
                </div>
              </div>

              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-green-900' : 'bg-green-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-green-300' : 'text-green-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Total Candidates</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{candidates.length}</h3>
                  </div>
                </div>
              </div>

              <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${darkMode ? 'bg-purple-900' : 'bg-purple-100'} mr-4`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 ${darkMode ? 'text-purple-300' : 'text-purple-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'} text-sm`}>Active Jobs</p>
                    <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                      {jobs.filter(job => job.recruitment_status === 'active').length}
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Welcome Section */}
            <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg shadow-md`}>
              <h2 className="text-lg font-semibold mb-4">Welcome, {currentUser?.username || 'User'}!</h2>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                This is your talent management dashboard. Use the sidebar to navigate to different sections of the application.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className={`${darkMode ? 'bg-blue-900 border-blue-800 text-blue-100' : 'bg-blue-50 border-blue-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-blue-200' : 'text-blue-800'}`}>Jobs</h3>
                  <p className={`${darkMode ? 'text-blue-300' : 'text-blue-600'} mt-1`}>Manage job listings and descriptions</p>
                  <button
                    onClick={() => setActiveSection('jobs')}
                    className={`mt-4 ${darkMode ? 'text-blue-300 hover:text-blue-200' : 'text-blue-600 hover:text-blue-800'} text-sm font-medium`}
                  >
                    View Jobs →
                  </button>
                </div>
                <div className={`${darkMode ? 'bg-green-900 border-green-800 text-green-100' : 'bg-green-50 border-green-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-green-200' : 'text-green-800'}`}>Candidates</h3>
                  <p className={`${darkMode ? 'text-green-300' : 'text-green-600'} mt-1`}>Track candidate profiles and applications</p>
                  <button
                    onClick={() => setActiveSection('candidates')}
                    className={`mt-4 ${darkMode ? 'text-green-300 hover:text-green-200' : 'text-green-600 hover:text-green-800'} text-sm font-medium`}
                  >
                    View Candidates →
                  </button>
                </div>
                <div className={`${darkMode ? 'bg-purple-900 border-purple-800 text-purple-100' : 'bg-purple-50 border-purple-100'} p-4 rounded-lg border`}>
                  <h3 className={`font-medium ${darkMode ? 'text-purple-200' : 'text-purple-800'}`}>Admin</h3>
                  <p className={`${darkMode ? 'text-purple-300' : 'text-purple-600'} mt-1`}>Manage users and system settings</p>
                  <button
                    onClick={() => navigate('/admin')}
                    className={`mt-4 ${darkMode ? 'text-purple-300 hover:text-purple-200' : 'text-purple-600 hover:text-purple-800'} text-sm font-medium`}
                  >
                    Go to Admin →
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'jobs':
        console.log('🚨 JOBS CASE: This should be the ONLY content rendered!');
        return (
          <div style={{ backgroundColor: 'orange', padding: '20px', border: '3px solid red' }}>
            <h1 style={{ color: 'white', fontSize: '24px' }}>🔥 JOBS SECTION - CLEAN TEST</h1>
            <p style={{ color: 'white' }}>If you see dashboard content below this, there's a MAJOR BUG!</p>
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Job Listings</h2>
              <div className="flex space-x-2">
                {/* Bulk Actions for Jobs */}
                {selectedJobs.length > 0 && (
                  <button
                    onClick={handleBulkDeleteJobs}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    Delete Selected ({selectedJobs.length})
                  </button>
                )}
                <button
                  onClick={() => setIsAddModalOpen(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Create New Job
                </button>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search jobs by title, manager, team, or TA..."
                  value={jobSearchTerm}
                  onChange={(e) => setJobSearchTerm(e.target.value)}
                  className={`w-full px-4 py-2 pl-10 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-400'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                {jobSearchTerm && (
                  <button
                    onClick={() => setJobSearchTerm('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              {jobSearchTerm && (
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Showing {filteredJobs.length} of {jobs.length} jobs
                </p>
              )}
            </div>

            {/* Jobs Bulk Selection */}
            {filteredJobs.length > 0 && (
              <div className="mb-4 flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectAllJobs}
                    onChange={handleSelectAllJobs}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Select All Jobs ({filteredJobs.length})
                  </span>
                </label>
                {selectedJobs.length > 0 && (
                  <span className={`ml-4 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                    {selectedJobs.length} selected
                  </span>
                )}
              </div>
            )}

            {/* Messages */}
            {error && (
              <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-100 text-green-700 p-3 rounded-md mb-4">
                {success}
              </div>
            )}

            {/* Jobs List */}
            {isLoading ? (
              <div className="text-center py-10">
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading jobs...</p>
              </div>
            ) : filteredJobs.length === 0 ? (
              <div className={`text-center py-10 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                  {jobSearchTerm ? `No jobs found matching "${jobSearchTerm}"` : 'No jobs found. Click "Create New Job" to post your first job opening.'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4">
                {filteredJobs.map(job => (
                  <JobCard
                    key={job.id}
                    job={job}
                    onEdit={(job) => {
                      setSelectedJob(job);
                      setTimeout(() => {
                        setIsEditModalOpen(true);
                      }, 0);
                    }}
                    onDelete={handleDeleteJob}
                    onSaveFilter={handleSaveFilter}
                    darkMode={darkMode}
                    isSelected={selectedJobs.includes(job.id)}
                    onSelect={handleJobSelection}
                  />
                ))}
              </div>
            )}

            {/* Job Modals */}
            <AddJobModal
              isOpen={isAddModalOpen}
              onClose={() => setIsAddModalOpen(false)}
              onJobAdded={handleAddJob}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />

            <EditJobModal
              isOpen={isEditModalOpen}
              onClose={() => setIsEditModalOpen(false)}
              onJobUpdated={handleEditJob}
              job={selectedJob}
              customJobTitles={customOptions.jobTitles}
              darkMode={darkMode}
            />
          </div>
        );

      case 'candidates':
        return (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Candidate Profiles</h2>
              <div className="flex space-x-2">
                {/* Bulk Actions for Candidates */}
                {selectedCandidates.length > 0 && (
                  <button
                    onClick={handleBulkDeleteCandidates}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    Delete Selected ({selectedCandidates.length})
                  </button>
                )}
                <button
                  onClick={() => setIsBulkUploadModalOpen(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Bulk Upload
                </button>
                <button
                  onClick={() => setIsAddCandidateModalOpen(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Add New Candidate
                </button>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search candidates by name, email, role, or status..."
                  value={candidateSearchTerm}
                  onChange={(e) => setCandidateSearchTerm(e.target.value)}
                  className={`w-full px-4 py-2 pl-10 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-400'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                {candidateSearchTerm && (
                  <button
                    onClick={() => setCandidateSearchTerm('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <svg className={`h-5 w-5 ${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              {candidateSearchTerm && (
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Showing {filteredCandidates.length} of {candidates.length} candidates
                </p>
              )}
            </div>

            {/* Candidates Bulk Selection */}
            {filteredCandidates.length > 0 && (
              <div className="mb-4 flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectAllCandidates}
                    onChange={handleSelectAllCandidates}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Select All Candidates ({filteredCandidates.length})
                  </span>
                </label>
                {selectedCandidates.length > 0 && (
                  <span className={`ml-4 text-sm ${darkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                    {selectedCandidates.length} selected
                  </span>
                )}
              </div>
            )}

            {/* Messages */}
            {candidateError && (
              <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
                {candidateError}
              </div>
            )}

            {success && (
              <div className="bg-green-100 text-green-700 p-3 rounded-md mb-4">
                {success}
              </div>
            )}

            {/* Candidates List */}
            {isLoadingCandidates ? (
              <div className="text-center py-10">
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading candidates...</p>
              </div>
            ) : !Array.isArray(filteredCandidates) || filteredCandidates.length === 0 ? (
              <div className={`text-center py-10 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md`}>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                  {candidateSearchTerm ? `No candidates found matching "${candidateSearchTerm}"` : 'No candidates found. Click "Add New Candidate" to create your first candidate profile.'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredCandidates.map(candidate => (
                  <CandidateCard
                    key={candidate.id}
                    candidate={candidate}
                    onEdit={(candidate) => {
                      setSelectedCandidate(candidate);
                      setIsEditCandidateModalOpen(true);
                    }}
                    onDelete={handleDeleteCandidate}
                    darkMode={darkMode}
                    isSelected={selectedCandidates.includes(candidate.id)}
                    onSelect={handleCandidateSelection}
                  />
                ))}
              </div>
            )}

            {/* Candidate Modals */}
            <AddCandidateModal
              isOpen={isAddCandidateModalOpen}
              onClose={() => setIsAddCandidateModalOpen(false)}
              onCandidateAdded={handleAddCandidate}
              darkMode={darkMode}
            />

            <EditCandidateModal
              isOpen={isEditCandidateModalOpen}
              onClose={() => setIsEditCandidateModalOpen(false)}
              onCandidateUpdated={handleEditCandidate}
              candidate={selectedCandidate}
              darkMode={darkMode}
            />

            <BulkUploadModal
              isOpen={isBulkUploadModalOpen}
              onClose={() => setIsBulkUploadModalOpen(false)}
              onCandidatesAdded={(newCandidates) => {
                setCandidates(prevCandidates => [...newCandidates, ...prevCandidates]);
                setSuccess(`${newCandidates.length} candidate(s) uploaded successfully!`);
              }}
              customJobTitles={customOptions.jobTitles}
              onCustomOptionsUpdate={handleCustomizeUpdate}
              darkMode={darkMode}
            />
          </div>
        );

      default:
        return (
          <div className={`text-center py-10 ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg shadow-md`}>
            <h2 className="text-xl font-semibold mb-4">Section Not Found</h2>
            <p>The requested section could not be found.</p>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg transform ${sidebarCollapsed ? '-translate-x-48' : 'translate-x-0'} transition-transform duration-300 ease-in-out`}>
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <h1 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>Talent Hero</h1>
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className={`p-2 rounded-md ${darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        <nav className="mt-5 px-2">
          <div className="space-y-1">
            <button
              onClick={() => setActiveSection('dashboard')}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${
                activeSection === 'dashboard'
                  ? `${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`
                  : `${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`
              }`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Dashboard
            </button>

            <button
              onClick={() => setActiveSection('jobs')}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${
                activeSection === 'jobs'
                  ? `${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`
                  : `${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`
              }`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Jobs
            </button>

            <button
              onClick={() => setActiveSection('candidates')}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${
                activeSection === 'candidates'
                  ? `${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`
                  : `${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`
              }`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Candidates
            </button>

            <button
              onClick={() => navigate('/admin')}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              Admin Panel
            </button>

            <button
              onClick={() => setShowCustomizeMenu(true)}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Customize Menu
            </button>

            <button
              onClick={toggleDarkMode}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}
            >
              {darkMode ? (
                <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              ) : (
                <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
              )}
              {darkMode ? 'Light Mode' : 'Dark Mode'}
            </button>

            <button
              onClick={logout}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full ${darkMode ? 'text-gray-300 hover:bg-gray-700 hover:text-white' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`}
            >
              <svg className="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Logout
            </button>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <main className={`ml-64 transition-all duration-300 ease-in-out p-6 ${sidebarCollapsed ? 'ml-16' : 'ml-64'}`}>
        <div key={`content-${activeSection}`} className="w-full">
          {renderContent()}
        </div>
      </main>

      {/* Global Modals */}
      {showCustomizeMenu && (
        <CustomizeMenu
          isOpen={showCustomizeMenu}
          onClose={() => setShowCustomizeMenu(false)}
          onUpdate={handleCustomizeUpdate}
          darkMode={darkMode}
        />
      )}

      {showDebugModal && (
        <DebugModal
          isOpen={showDebugModal}
          onClose={() => setShowDebugModal(false)}
          debugInfo={debugInfo}
          darkMode={darkMode}
        />
      )}
    </div>
  );
};

export default Dashboard;
