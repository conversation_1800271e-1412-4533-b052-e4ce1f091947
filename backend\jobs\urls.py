from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import JobViewSet, bulk_delete_jobs
from .views_qa import GenerateQAView

router = DefaultRouter()
router.register(r'', JobViewSet)

urlpatterns = [
    path('generate-qa/', GenerateQAView.as_view(), name='generate-qa'),
    path('bulk-delete/', bulk_delete_jobs, name='bulk-delete-jobs'),
    path('', include(router.urls)),
]
