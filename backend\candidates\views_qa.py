import os
import json
import logging
import time
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from jobs.models import Job

# Configure logging
logger = logging.getLogger(__name__)

class GenerateQAView(APIView):
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        start_time = time.time()
        request_id = f"qa_req_{int(start_time)}"
        logger.info(f"[{request_id}] Starting question generation request from user {request.user.username}")
        
        try:
            job_id = request.data.get('job_id')
            num_questions = min(max(1, int(request.data.get('num_questions', 5))), 20)
            
            logger.info(f"[{request_id}] Parameters: job_id={job_id}, num_questions={num_questions}")
            
            if not job_id:
                logger.warning(f"[{request_id}] Missing job_id parameter")
                return Response({'detail': 'Job ID is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                job = Job.objects.get(id=job_id)
                logger.info(f"[{request_id}] Found job: {job.title_display} (ID: {job.id})")
            except Job.DoesNotExist:
                logger.warning(f"[{request_id}] Job with ID {job_id} not found")
                return Response({'detail': 'Job not found'}, status=status.HTTP_404_NOT_FOUND)
            
            # Determine input for the Ollama model
            input_text = ""
            if job.description_document and os.path.exists(job.description_document.path):
                try:
                    logger.info(f"[{request_id}] Reading job description document from {job.description_document.path}")
                    with open(job.description_document.path, 'r', encoding='utf-8') as file:
                        input_text = file.read()
                    logger.debug(f"[{request_id}] Successfully read job description document ({len(input_text)} characters)")
                except Exception as e:
                    logger.error(f"[{request_id}] Error reading job description document: {e}")
                    input_text = f"Job Title: {job.title_display}"
            else:
                logger.info(f"[{request_id}] No description document found, using job title only")
                input_text = f"Job Title: {job.title_display}"
            
            # Prepare prompt for Ollama
            prompt = f"""
            Based on the following job description, generate {num_questions} interview questions and their expected answers. 
            The questions should test technical knowledge and competencies required for the role.
            
            JOB DESCRIPTION:
            {input_text}
            
            Format your response as a valid JSON array with 'question' and 'answer' fields for each item.
            Example:
            [
                {{
                    "question": "What is dependency injection and why is it useful?",
                    "answer": "Dependency injection is a design pattern that allows a class to receive its dependencies from external sources rather than creating them itself. It's useful because it promotes loose coupling, easier testing, and separation of concerns."
                }}
            ]
            """
            
            logger.debug(f"[{request_id}] Generated prompt with {len(prompt)} characters")
            
            # Call Ollama API
            import requests
            
            try:
                logger.info(f"[{request_id}] Sending request to Ollama API")
                api_start_time = time.time()
                
                response = requests.post('http://localhost:11434/api/generate', 
                    json={
                        'model': 'cogito:3b',
                        'prompt': prompt,
                        'stream': False
                    },
                    timeout=60
                )
                
                api_duration = time.time() - api_start_time
                logger.info(f"[{request_id}] Ollama API response received in {api_duration:.2f} seconds with status code {response.status_code}")
                
                response.raise_for_status()
                
                result = response.json()
                model_response = result.get('response', '')
                
                logger.debug(f"[{request_id}] Raw model response: {model_response[:200]}...")
                
                # Try to extract JSON from the response
                try:
                    # Find JSON in the response (it might be surrounded by text or markdown code blocks)
                    json_start = model_response.find('[')
                    json_end = model_response.rfind(']') + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_str = model_response[json_start:json_end]
                        logger.debug(f"[{request_id}] Extracted JSON: {json_str[:200]}...")
                        
                        questions = json.loads(json_str)
                        logger.info(f"[{request_id}] Successfully parsed {len(questions)} questions")
                    else:
                        # Fallback if no JSON was found
                        logger.error(f"[{request_id}] Failed to find JSON in response: {model_response[:500]}")
                        return Response({
                            'detail': 'Failed to generate properly formatted questions',
                            'raw_response': model_response[:500]  # Truncate for logging
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        
                except json.JSONDecodeError as json_err:
                    logger.error(f"[{request_id}] JSON decode error: {json_err}, response: {model_response[:500]}")
                    return Response({
                        'detail': 'Failed to parse the generated questions',
                        'raw_response': model_response[:500]  # Truncate for logging
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
                total_duration = time.time() - start_time
                logger.info(f"[{request_id}] Request completed successfully in {total_duration:.2f} seconds")
                return Response({'questions': questions})
                
            except requests.exceptions.RequestException as e:
                logger.error(f"[{request_id}] Error calling Ollama API: {e}")
                return Response({
                    'detail': 'Failed to connect to the AI model service. Please make sure Ollama is running.'
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
        except Exception as e:
            logger.exception(f"[{request_id}] Unexpected error in GenerateQAView: {str(e)}")
            return Response({
                'detail': f'An unexpected error occurred: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
