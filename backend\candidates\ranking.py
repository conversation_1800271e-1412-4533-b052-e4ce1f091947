import requests
import json
import os
import logging
import re
from django.conf import settings
from .models import CandidateRanking

# Configure logging
logger = logging.getLogger(__name__)

# URL for Ollama API
OLLAMA_API_URL = "http://localhost:11434/api/generate"
OLLAMA_MODEL = "cogito:3b"

def get_file_content(file_path):
    """Read the content of a file from the media directory."""
    if not file_path:
        return None
        
    full_path = os.path.join(settings.MEDIA_ROOT, file_path)
    if not os.path.exists(full_path):
        return None
        
    try:
        with open(full_path, 'r') as file:
            return file.read()
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def extract_skills_from_text(text):
    """
    Helper function to extract potential skills from text.
    Basic implementation - looks for common tech skills.
    """
    # Common tech skills to look for
    common_skills = [
        "python", "java", "javascript", "react", "django", "node.js", "angular", "vue", 
        "html", "css", "sql", "postgresql", "mysql", "mongodb", "aws", "azure", "gcp",
        "docker", "kubernetes", "git", "ci/cd", "agile", "scrum", "rest api", "graphql"
    ]
    
    # Convert text to lowercase for case-insensitive matching
    text_lower = text.lower()
    
    # Find all mentioned skills
    found_skills = []
    for skill in common_skills:
        if skill.lower() in text_lower:
            found_skills.append(skill)
    
    return found_skills

def rank_candidate(job_description_content, resume_content, candidate_name):
    """
    Compare a candidate's resume against a job description using Ollama model.
    Returns a score and reasoning.
    """
    if not job_description_content or not resume_content:
        return {
            "score": 0, 
            "reasoning": "Missing job description or resume content."
        }
        
    # Prompt for the model
    prompt = f"""
    Task: Evaluate and rank a candidate's resume against a job description.
    
    JOB DESCRIPTION:
    {job_description_content}
    
    CANDIDATE RESUME:
    {resume_content}
    
    Please analyze how well the candidate '{candidate_name}' matches the job requirements.
    
    In your analysis:
    1. Identify specific skills mentioned in the job description
    2. Determine which of these skills the candidate demonstrates in their resume
    3. Note which required skills appear to be missing from the candidate's profile
    
    Provide a score from 1 to 10 (where 10 is perfect match) and explain your reasoning.
    
    Format your response as a JSON object with these fields:
    1. 'score': A number from 1 to 10
    2. 'reasoning': A brief explanation (max 150 words) for your score
    3. 'matching_skills': An array of skills the candidate has that match job requirements
    4. 'missing_skills': An array of skills required by the job that the candidate lacks
    
    Response (JSON):
    """
    
    try:
        # Increase timeout to 60 seconds since Ollama is taking longer to respond
        # Call Ollama API
        response = requests.post(
            OLLAMA_API_URL,
            json={
                "model": OLLAMA_MODEL,
                "prompt": prompt,
                "stream": False
            },
            timeout=60  # Increased from 30 to 60 seconds
        )
        
        if response.status_code != 200:
            return {
                "score": 0,
                "reasoning": f"Error: Ollama API returned status code {response.status_code}"
            }
            
        # Parse the response to extract the JSON
        try:
            response_text = response.json().get('response', '').strip()
            
            # Find and extract the JSON part from the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)
                
                # Ensure we have the expected fields
                if 'score' not in result:
                    result['score'] = 0
                if 'reasoning' not in result:
                    result['reasoning'] = "Invalid response format from AI model."
                if 'matching_skills' not in result:
                    result['matching_skills'] = []
                if 'missing_skills' not in result:
                    result['missing_skills'] = []
                    
                # Ensure score is a number between 1 and 10
                try:
                    result['score'] = int(result['score'])
                    if result['score'] < 1:
                        result['score'] = 1
                    elif result['score'] > 10:
                        result['score'] = 10
                except:
                    result['score'] = 0
                    
                return result
            else:
                return {
                    "score": 0,
                    "reasoning": "Could not parse JSON response from AI model."
                }
                
        except Exception as e:
            return {
                "score": 0,
                "reasoning": f"Error parsing AI response: {str(e)}"
            }
            
    except requests.ConnectionError:
        return {
            "score": 0,
            "reasoning": "Error: Could not connect to Ollama API. Please make sure Ollama is installed and running on your system."
        }
    except requests.Timeout:
        return {
            "score": 0,
            "reasoning": "Error: Connection to Ollama API timed out. The model might be taking too long to respond or your system might not have enough resources for this model. Try using a smaller model or increasing available system resources."
        }
    except requests.RequestException as e:
        return {
            "score": 0,
            "reasoning": f"Error connecting to Ollama API: {str(e)}"
        }

def rank_candidates_for_job(job, candidates, force_refresh=False):
    """
    Rank all candidates against a job description.
    
    Args:
        job: Job model instance
        candidates: List of Candidate model instances
        force_refresh: If True, re-evaluate all candidates even if they have existing rankings
        
    Returns:
        List of dicts with candidate info and rankings
    """
    rankings = []
    
    for candidate in candidates:
        # Check if we already have a ranking for this candidate-job pair
        existing_ranking = None
        if not force_refresh:
            try:
                existing_ranking = CandidateRanking.objects.get(candidate=candidate, job=job)
                logger.info(f"Found existing ranking for {candidate.name} and {job.title}")
            except CandidateRanking.DoesNotExist:
                pass
        
        if existing_ranking and not force_refresh:
            # Use existing ranking
            ranking_dict = {
                "candidate_id": candidate.id,
                "candidate_name": candidate.name,
                "score": existing_ranking.score,
                "reasoning": existing_ranking.reasoning,
                "last_updated": existing_ranking.updated_at.strftime("%Y-%m-%d %H:%M"),
                "is_cached": True  # Flag to indicate this is a cached result
            }
        else:
            # Get job description content
            job_desc_content = ""
            if job.description_document:
                try:
                    # Try to read the content of the document
                    job_desc_content = f"Job Description: {job.description_document.path}\n"
                except Exception as e:
                    # If we can't read the file, use other job details
                    logger.error(f"Error reading job description document: {str(e)}")
                    job_desc_content = ""
            
            # If no document or couldn't read it, use job details as fallback
            if not job_desc_content:
                job_desc_content = (
                    f"Job Title: {job.get_title_display()}\n"
                    f"Positions Available: {job.positions_available or 'Not specified'}\n"
                    f"Hiring Team: {job.hiring_team or 'Not specified'}\n"
                    f"Hiring Manager: {job.hiring_manager or 'Not specified'}\n"
                    f"Recruitment Status: {job.get_recruitment_status_display() or 'Active'}\n"
                )
            
            # Get candidate resume content
            resume_content = ""
            if candidate.resume:
                try:
                    # Try to access the resume filename property (not method)
                    resume_content = f"Resume: {candidate.resume_filename}\n"
                except Exception as e:
                    logger.error(f"Error accessing resume filename: {str(e)}")
                    resume_content = ""
            
            # Add candidate profile information
            resume_content += (
                f"Name: {candidate.name}\n"
                f"Preferred Role: {candidate.get_preferred_role_display() if candidate.preferred_role else 'Not specified'}\n"
                f"Total Experience: {candidate.total_experience or 'Not specified'} years\n"
                f"Hiring Status: {candidate.get_hiring_status_display() if candidate.hiring_status else 'Not specified'}\n"
                f"Comments: {candidate.comments or 'None'}\n"
            )
            
            # Get ranking
            result = rank_candidate(job_desc_content, resume_content, candidate.name)
            
            # Save the ranking to the database
            ranking_obj, created = CandidateRanking.objects.update_or_create(
                candidate=candidate,
                job=job,
                defaults={
                    'score': result.get('score', 0),
                    'reasoning': result.get('reasoning', ''),
                    'matching_skills': result.get('matching_skills', []),
                    'missing_skills': result.get('missing_skills', [])
                }
            )
            
            # Create response dict
            ranking_dict = {
                "candidate_id": candidate.id,
                "candidate_name": candidate.name,
                "score": result.get('score', 0),
                "reasoning": result.get('reasoning', ''),
                "last_updated": ranking_obj.updated_at.strftime("%Y-%m-%d %H:%M"),
                "is_cached": False,  # Flag to indicate this is a fresh result
                "matching_skills": result.get('matching_skills', []),
                "missing_skills": result.get('missing_skills', [])
            }
        
        rankings.append(ranking_dict)
    
    # Sort by score, highest first
    rankings.sort(key=lambda x: x.get('score', 0), reverse=True)
    
    return rankings
