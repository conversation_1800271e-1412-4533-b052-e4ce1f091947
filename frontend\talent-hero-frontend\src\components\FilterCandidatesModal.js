import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

const FilterCandidatesModal = ({ job, onClose, onSaveFilter, isOpen, darkMode = false }) => {
  const [selectedStatuses, setSelectedStatuses] = useState([]);
  const [candidates, setCandidates] = useState([]);
  const [filteredCandidates, setFilteredCandidates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filterName, setFilterName] = useState("");
  
  const hiringStatusOptions = [
    { value: 'no_engagement', label: 'No Engagement' },
    { value: 'l1_scheduled', label: 'L1 Scheduled' },
    { value: 'l1_attended', label: 'L1 Attended' },
    { value: 'l1_dropoff', label: 'L1 Dropoff' },
    { value: 'l1_hold', label: 'L1 Hold' },
    { value: 'l1_rejected', label: 'L1 Rejected' },
    { value: 'l1_selected', label: 'L1 Selected' },
    { value: 'l2_scheduled', label: 'L2 Scheduled' },
    { value: 'l2_attended', label: 'L2 Attended' },
    { value: 'l2_dropoff', label: 'L2 Dropoff' },
    { value: 'l2_hold', label: 'L2 Hold' },
    { value: 'l2_rejected', label: 'L2 Rejected' },
    { value: 'l2_selected', label: 'L2 Selected' },
    { value: 'l3_scheduled', label: 'L3 Scheduled' },
    { value: 'l3_attended', label: 'L3 Attended' },
    { value: 'l3_dropoff', label: 'L3 Dropoff' },
    { value: 'l3_hold', label: 'L3 Hold' },
    { value: 'l3_rejected', label: 'L3 Rejected' },
    { value: 'l3_selected', label: 'L3 Selected' },
  ];

  useEffect(() => {
    if (isOpen) {
      fetchCandidates();
    }
  }, [isOpen]);

  useEffect(() => {
    if (candidates.length > 0) {
      applyFilters();
    }
  }, [selectedStatuses, candidates]);

  const fetchCandidates = async () => {
    if (!job.title) {
      setError('Job title is required for filtering candidates');
      return;
    }
    
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/candidates/`, {
        headers: { Authorization: `Token ${token}` }
      });
      setCandidates(response.data);
      setIsLoading(false);
    } catch (err) {
      setError('Failed to fetch candidates');
      setIsLoading(false);
    }
  };

  const handleStatusToggle = (statusValue) => {
    setSelectedStatuses(prevSelected => {
      if (prevSelected.includes(statusValue)) {
        return prevSelected.filter(s => s !== statusValue);
      } else {
        return [...prevSelected, statusValue];
      }
    });
  };

  const applyFilters = () => {
    // First filter by role match
    let filtered = candidates.filter(candidate => {
      // Match either preferred role or optional roles with job title
      return (
        candidate.preferred_role === job.title || 
        candidate.optional_roles === job.title
      );
    });

    // Then filter by selected statuses if any are selected
    if (selectedStatuses.length > 0) {
      filtered = filtered.filter(candidate => 
        selectedStatuses.includes(candidate.hiring_status)
      );
    }

    setFilteredCandidates(filtered);
  };

  const handleRefreshFilter = () => {
    fetchCandidates();
  };

  const handleResetFilter = () => {
    setSelectedStatuses([]);
  };

  const handleSaveFilter = () => {
    if (!filterName.trim()) {
      alert('Please provide a name for this filter');
      return;
    }
    
    const filterData = {
      id: Date.now().toString(),
      name: filterName,
      jobId: job.id,
      jobTitle: job.title_display,
      statuses: selectedStatuses,
      candidates: filteredCandidates,
      createdAt: new Date().toISOString()
    };
    
    // Save to localStorage
    const savedFilters = JSON.parse(localStorage.getItem('savedCandidateFilters') || '[]');
    localStorage.setItem('savedCandidateFilters', JSON.stringify([...savedFilters, filterData]));
    
    if (onSaveFilter) {
      onSaveFilter(filterData);
    }
    
    onClose();
  };

  // If job has no title, show error message
  if (!job.title && isOpen) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
        <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-xl p-6 max-w-lg m-4`}>
          <h3 className="text-xl font-semibold mb-4">Error</h3>
          <p className={`${darkMode ? 'text-red-300' : 'text-red-600'} mb-6`}>
            Job title is required for filtering candidates.
          </p>
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isOpen) return null;

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-xl max-w-3xl w-full m-4`}>
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Filter Candidates for {job.title_display}</h2>
                <button
                  onClick={onClose}
                  className={`${darkMode ? 'text-gray-300 hover:text-gray-100' : 'text-gray-500 hover:text-gray-700'}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {error && (
                <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-50 text-red-700'} p-4 rounded mb-4`}>
                  {error}
                </div>
              )}
              
              <div className="mb-4">
                <label htmlFor="filterName" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                  Filter Name
                </label>
                <input
                  type="text"
                  id="filterName"
                  value={filterName}
                  onChange={(e) => setFilterName(e.target.value)}
                  placeholder="Enter a name for this filter"
                  className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-800'} rounded`}
                />
              </div>
              
              <div className="mb-4">
                <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                  Select Hiring Statuses
                </h4>
                <div className="grid grid-cols-3 gap-2">
                  {hiringStatusOptions.map(status => (
                    <div key={status.value} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`status-${status.value}`}
                        checked={selectedStatuses.includes(status.value)}
                        onChange={() => handleStatusToggle(status.value)}
                        className={`mr-2 ${darkMode ? 'accent-blue-500' : ''}`}
                      />
                      <label htmlFor={`status-${status.value}`} className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {status.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mb-6">
                <h4 className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                  Filtered Candidates ({filteredCandidates.length})
                </h4>
                
                {isLoading ? (
                  <div className="text-center py-4">
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading candidates...</p>
                  </div>
                ) : filteredCandidates.length === 0 ? (
                  <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded text-center`}>
                    <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>No matching candidates found</p>
                  </div>
                ) : (
                  <div className={`${darkMode ? 'bg-gray-700' : 'bg-gray-50'} p-2 rounded max-h-60 overflow-y-auto`}>
                    {filteredCandidates.map(candidate => (
                      <div 
                        key={candidate.id} 
                        className={`p-2 ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded shadow-sm mb-2 flex justify-between items-center`}
                      >
                        <div>
                          <p className="font-medium">{candidate.name || "Unnamed Candidate"}</p>
                          <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {candidate.preferred_role_display}, {candidate.hiring_status_display}
                          </p>
                        </div>
                        <div>
                          <span className={`inline-block px-2 py-1 text-xs rounded-full ${darkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'}`}>
                            {candidate.total_experience ? `${candidate.total_experience} Yrs` : 'No Exp'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="flex justify-between">
                <div className="space-x-2">
                  <button
                    onClick={handleRefreshFilter}
                    className={`px-3 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded text-sm`}
                  >
                    Refilter
                  </button>
                  <button
                    onClick={handleResetFilter}
                    className={`px-3 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded text-sm`}
                  >
                    Reset
                  </button>
                </div>
                <div className="space-x-2">
                  <button
                    onClick={onClose}
                    className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveFilter}
                    className={`px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 ${(filteredCandidates.length === 0 || !filterName.trim()) ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={filteredCandidates.length === 0 || !filterName.trim()}
                  >
                    Save Filter
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FilterCandidatesModal;
