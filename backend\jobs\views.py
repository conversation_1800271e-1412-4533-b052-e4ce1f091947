from django.shortcuts import render
from rest_framework import viewsets, parsers, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from .models import Job
from .serializers import JobSerializer

# Create your views here.

class JobViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing jobs
    """
    queryset = Job.objects.all().order_by('-created_at')
    serializer_class = JobSerializer
    parser_classes = [parsers.MultiPartParser, parsers.FormParser]
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Job.objects.all().order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def bulk_delete_jobs(request):
    """
    Bulk delete jobs
    """
    try:
        job_ids = request.data.get('job_ids', [])

        if not job_ids:
            return Response(
                {'error': 'No job IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Delete the jobs
        deleted_count = Job.objects.filter(id__in=job_ids).count()
        Job.objects.filter(id__in=job_ids).delete()

        return Response({
            'message': f'{deleted_count} job(s) deleted successfully',
            'deleted_count': deleted_count
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
