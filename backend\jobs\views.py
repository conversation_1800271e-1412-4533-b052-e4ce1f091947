from django.shortcuts import render
from rest_framework import viewsets, parsers, permissions
from rest_framework.response import Response
from .models import Job
from .serializers import JobSerializer

# Create your views here.

class JobViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing jobs
    """
    queryset = Job.objects.all().order_by('-created_at')
    serializer_class = JobSerializer
    parser_classes = [parsers.MultiPartParser, parsers.FormParser]
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Job.objects.all().order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
