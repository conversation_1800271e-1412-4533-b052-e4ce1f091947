import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import FileUpload from './FileUpload';

const AddJobModal = ({ isOpen, onClose, onJobAdded, customJobTitles, darkMode = false }) => {
  const initialJobData = {
    title: '',
    hiring_manager: '',
    ta_incharge: '',
    hiring_team: '',
    positions_available: '',
    recruitment_status: 'active',
    description_document: null
  };

  const [jobData, setJobData] = useState(initialJobData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [jobTitleOptions, setJobTitleOptions] = useState([]);

  useEffect(() => {
    if (isOpen) {
      setJobData(initialJobData);
      setError('');
      loadJobTitleOptions();
    }
  }, [isOpen]);

  const loadJobTitleOptions = () => {
    let options;
    
    // If customJobTitles prop is provided, use it
    if (customJobTitles && customJobTitles.length > 0) {
      options = customJobTitles;
    } else {
      // Otherwise, try to get from localStorage
      const savedOptions = JSON.parse(localStorage.getItem('customJobTitles') || '[]');
      
      // If localStorage has options, use them
      if (savedOptions.length > 0) {
        options = savedOptions;
      } else {
        // Fallback to default options
        options = [
          { value: 'devops', label: 'DevOps Engineer' },
          { value: 'data_analyst', label: 'Data Analyst' },
          { value: 'qa_testing', label: 'QA Testing Engineer' },
          { value: 'java_fullstack', label: 'Java Full Stack Engineer' },
          { value: 'python_developer', label: 'Python Developer' },
          { value: 'servicenow', label: 'ServiceNow Specialist' },
          { value: 'rpa_developer', label: 'RPA Developer' }
        ];
      }
    }
    
    setJobTitleOptions(options);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setJobData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (file) => {
    setJobData(prev => ({
      ...prev,
      description_document: file
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      const token = localStorage.getItem('token');

      // Create FormData object to handle file upload
      const formData = new FormData();
      
      // Add all job data fields to FormData
      Object.keys(jobData).forEach(key => {
        if (key === 'description_document' && jobData[key]) {
          formData.append(key, jobData[key]);
        } else if (jobData[key] !== null && jobData[key] !== undefined) {
          formData.append(key, jobData[key]);
        }
      });

      // Make the API request
      const response = await axios.post(`${API_URL}/api/jobs/`, formData, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      // Add the new job to the list
      if (onJobAdded) {
        onJobAdded(response.data);
      }

      // Close the modal
      onClose();

    } catch (error) {
      console.error('Error creating job:', error);
      setError('Failed to create job. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white'} p-6 rounded-lg w-full max-w-lg mx-auto`}>
        <h2 className="text-xl font-semibold mb-4">Create New Job</h2>
        
        {error && (
          <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-700'} p-3 rounded-md mb-4`}>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Job Title */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Job Title
              </label>
              <select
                name="title"
                value={jobData.title}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                required
              >
                <option value="">Select Job Title</option>
                {jobTitleOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Hiring Manager */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Hiring Manager
              </label>
              <input
                type="text"
                name="hiring_manager"
                value={jobData.hiring_manager}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Name of hiring manager"
              />
            </div>

            {/* TA Incharge */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                TA Incharge
              </label>
              <input
                type="text"
                name="ta_incharge"
                value={jobData.ta_incharge}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Name of TA incharge"
              />
            </div>

            {/* Hiring Team */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Hiring Team
              </label>
              <input
                type="text"
                name="hiring_team"
                value={jobData.hiring_team}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="e.g. Engineering, HR, Management"
              />
            </div>

            {/* Positions Available */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Positions Available
              </label>
              <input
                type="number"
                name="positions_available"
                value={jobData.positions_available}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Number of positions"
                min="1"
              />
            </div>

            {/* Recruitment Status */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Recruitment Status
              </label>
              <select
                name="recruitment_status"
                value={jobData.recruitment_status}
                onChange={handleChange}
                className={`w-full p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'border-gray-300 text-gray-800'} rounded focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="active">Active</option>
                <option value="hold">On Hold</option>
                <option value="stopped">Stopped</option>
              </select>
            </div>

            {/* Description Document */}
            <div>
              <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
                Description Document
              </label>
              <FileUpload 
                onFileSelect={handleFileChange} 
                label="Upload JD (PDF, DOC, DOCX)" 
                acceptTypes=".pdf,.doc,.docx"
                darkMode={darkMode}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Job'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddJobModal;
