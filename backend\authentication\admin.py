from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.contrib import messages
from .models import User

def make_inactive(modeladmin, request, queryset):
    """Bulk action to deactivate selected users"""
    updated = queryset.update(is_active=False)
    messages.success(request, f'{updated} user(s) have been deactivated.')
make_inactive.short_description = "Deactivate selected users"

def make_active(modeladmin, request, queryset):
    """Bulk action to activate selected users"""
    updated = queryset.update(is_active=True)
    messages.success(request, f'{updated} user(s) have been activated.')
make_active.short_description = "Activate selected users"

def delete_selected_users(modeladmin, request, queryset):
    """Bulk action to delete selected users"""
    count = queryset.count()
    queryset.delete()
    messages.success(request, f'{count} user(s) have been deleted.')
delete_selected_users.short_description = "Delete selected users"

class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_active_status', 'is_admin', 'is_staff', 'date_joined')
    list_filter = ('is_active', 'is_admin', 'is_staff', 'is_superuser', 'date_joined')
    fieldsets = (
        (None, {'fields': ('username', 'email', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name')}),
        ('Permissions', {'fields': ('is_active', 'is_admin', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'is_active', 'is_admin', 'is_staff', 'is_superuser'),
        }),
    )
    search_fields = ('username', 'email', 'first_name', 'last_name')
    ordering = ('username',)  # Ascending order by username
    actions = [make_inactive, make_active, delete_selected_users]

    def is_active_status(self, obj):
        """Display active status with colored indicator"""
        if obj.is_active:
            return format_html('<span style="color: green;">●</span> Active')
        else:
            return format_html('<span style="color: red;">●</span> Inactive')
    is_active_status.short_description = 'Status'
    is_active_status.admin_order_field = 'is_active'

admin.site.register(User, CustomUserAdmin)
