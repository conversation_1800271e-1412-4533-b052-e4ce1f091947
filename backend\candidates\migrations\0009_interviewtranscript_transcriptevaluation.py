# Generated by Django 5.2 on 2025-04-14 21:26

import candidates.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('candidates', '0008_delete_opportunity'),
    ]

    operations = [
        migrations.CreateModel(
            name='InterviewTranscript',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('L1', 'Level 1'), ('L2', 'Level 2'), ('L3', 'Level 3'), ('L4', 'Level 4')], max_length=2)),
                ('transcript_file', models.FileField(upload_to=candidates.models.transcript_upload_path)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('candidate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transcripts', to='candidates.candidate')),
            ],
            options={
                'unique_together': {('candidate', 'level')},
            },
        ),
        migrations.CreateModel(
            name='TranscriptEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.IntegerField(default=0)),
                ('improvement_areas', models.TextField(blank=True)),
                ('plagiarism_concerns', models.TextField(blank=True)),
                ('detailed_report', models.TextField(blank=True)),
                ('report_file', models.FileField(blank=True, null=True, upload_to=candidates.models.evaluation_report_path)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transcript', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='evaluation', to='candidates.interviewtranscript')),
            ],
        ),
    ]
