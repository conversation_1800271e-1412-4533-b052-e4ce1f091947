from django.shortcuts import render
from rest_framework import viewsets, permissions, status, parsers
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import Candidate, CandidateRanking, InterviewTranscript, TranscriptEvaluation
from .serializers import (
    CandidateSerializer, CandidateRankingSerializer, InterviewTranscriptSerializer,
    TranscriptEvaluationSerializer
)
from .ranking import rank_candidates_for_job, extract_skills_from_text
from jobs.models import Job
import os
import uuid
import json
import PyPDF2
from django.conf import settings
import requests
from datetime import datetime
from django.http import HttpResponse
import logging
import traceback
from django.db import connection
import re

# Set up logging
logger = logging.getLogger(__name__)

# Create your views here.

class CandidateViewSet(viewsets.ModelViewSet):
    queryset = Candidate.objects.all().order_by('-created_at')
    serializer_class = CandidateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """Override create to add logging"""
        logger.info("Creating new candidate")
        logger.info(f"Request data: {request.data}")

        try:
            # Continue with standard create process
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error creating candidate: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def update(self, request, *args, **kwargs):
        """Override update to add logging"""
        logger.info(f"Updating candidate {kwargs.get('pk')}")
        logger.info(f"Request data: {request.data}")

        try:
            # Continue with standard update process
            return super().update(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Error updating candidate: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def list(self, request, *args, **kwargs):
        """Override list to add logging"""
        logger.info("Fetching candidates list")
        logger.info(f"User: {request.user}")

        try:
            # Continue with standard list process
            response = super().list(request, *args, **kwargs)
            logger.info(f"Successfully fetched {len(response.data)} candidates")
            return response
        except Exception as e:
            logger.error(f"Error fetching candidates: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    def perform_create(self, serializer):
        logger.info(f"Performing create with validated data: {serializer.validated_data}")

        # Log interview levels data for debugging
        interview_levels_raw = self.request.data.get('interview_levels', '[]')
        logger.info(f"Interview levels raw data: {interview_levels_raw}")
        logger.info(f"Interview levels type: {type(interview_levels_raw)}")

        serializer.save(created_by=self.request.user)

    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        return context

@api_view(['POST', 'GET'])
@permission_classes([IsAuthenticated])
def rank_candidates(request):
    """
    Rank candidates against a job description using Ollama.

    POST method accepts:
    {
        "job_id": 123,
        "candidate_ids": [1, 2, 3],
        "force_refresh": false  // Optional, defaults to false
    }

    GET method accepts:
    ?job_id=123 - to retrieve existing rankings for a specific job

    Returns a sorted list of candidates with their scores.
    """
    if request.method == 'GET':
        # Get rankings for a specific job
        job_id = request.query_params.get('job_id')
        if not job_id:
            return Response({"error": "Job ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            job = Job.objects.get(id=job_id)
        except Job.DoesNotExist:
            return Response({"error": "Job not found"}, status=status.HTTP_404_NOT_FOUND)

        # Get all rankings for this job
        rankings = CandidateRanking.objects.filter(job=job).select_related('candidate')

        if not rankings.exists():
            return Response({"error": "No rankings found for this job"}, status=status.HTTP_404_NOT_FOUND)

        # Format the response
        result = []
        for ranking in rankings:
            # Get candidate and JSON fields for skills
            candidate = ranking.candidate

            # We now have the matching and missing skills stored in the database
            # So we can directly use them
            result.append({
                "candidate_id": ranking.candidate.id,
                "candidate_name": ranking.candidate.name,
                "score": ranking.score,
                "reasoning": ranking.reasoning,
                "last_updated": ranking.updated_at.strftime("%Y-%m-%d %H:%M"),
                "is_cached": True,
                "matching_skills": ranking.matching_skills,
                "missing_skills": ranking.missing_skills
            })

        # Sort by score
        result.sort(key=lambda x: x["score"], reverse=True)

        return Response(result, status=status.HTTP_200_OK)

    # For POST requests
    job_id = request.data.get('job_id')
    candidate_ids = request.data.get('candidate_ids', [])
    force_refresh = request.data.get('force_refresh', False)

    if not job_id:
        return Response({"error": "Job ID is required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        job = Job.objects.get(id=job_id)
    except Job.DoesNotExist:
        return Response({"error": "Job not found"}, status=status.HTTP_404_NOT_FOUND)

    # Get candidates
    if candidate_ids:
        candidates = Candidate.objects.filter(id__in=candidate_ids)
    else:
        # If no candidates specified, find candidates with the matching role
        candidates = Candidate.objects.filter(preferred_role=job.title) | Candidate.objects.filter(optional_roles=job.title)

    if not candidates.exists():
        return Response({"error": "No matching candidates found"}, status=status.HTTP_404_NOT_FOUND)

    # Rank candidates
    rankings = rank_candidates_for_job(job, candidates, force_refresh=force_refresh)

    return Response(rankings, status=status.HTTP_200_OK)

class InterviewTranscriptViewSet(viewsets.ModelViewSet):
    """
    API endpoints for managing interview transcripts.
    """
    queryset = InterviewTranscript.objects.all()
    serializer_class = InterviewTranscriptSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [parsers.MultiPartParser, parsers.FormParser]
    http_method_names = ['get', 'post', 'head', 'options']

    def get_queryset(self):
        """Filter transcripts by candidate if candidate_id is provided"""
        logger.info("GetQuerySet called for InterviewTranscriptViewSet")
        candidate_id = self.request.query_params.get('candidate_id', None)

        if candidate_id:
            logger.info(f"Filtering transcripts for candidate_id: {candidate_id}")
            try:
                # Try to convert candidate_id to integer
                candidate_id = int(candidate_id)
                queryset = InterviewTranscript.objects.filter(candidate_id=candidate_id)
                transcript_count = queryset.count()
                logger.info(f"Found {transcript_count} transcripts for candidate {candidate_id}")

                # Log transcript details for debugging
                for transcript in queryset:
                    logger.info(f"Transcript ID: {transcript.id}, Level: {transcript.level}, File: {transcript.transcript_file.name if transcript.transcript_file else 'None'}")

                return queryset
            except ValueError:
                logger.error(f"Invalid candidate_id format: {candidate_id}")
                return InterviewTranscript.objects.none()
            except Exception as e:
                logger.error(f"Error filtering transcripts: {str(e)}")
                logger.error(traceback.format_exc())
                return InterviewTranscript.objects.none()
        else:
            logger.info("No candidate_id provided, returning all transcripts")
            return InterviewTranscript.objects.all()

    @action(detail=False, methods=['post'])
    def upload(self, request):
        """Custom action for uploading transcripts"""
        logger.info("Upload action called")
        logger.info(f"Request data: {request.data}")
        logger.info(f"Request FILES: {request.FILES}")

        # Detailed validation and debugging
        if 'transcript_file' not in request.FILES:
            logger.error("Missing transcript_file in request.FILES")
            logger.debug(f"Full request content: {request.content_type}")
            logger.debug(f"Content parts: {[k for k in request.FILES.keys()]}")
            return Response({"error": "No transcript file found in request"},
                           status=status.HTTP_400_BAD_REQUEST)

        candidate_id = request.data.get('candidate')
        if not candidate_id:
            logger.error("Missing candidate ID")
            return Response({"error": "Candidate ID is required"},
                           status=status.HTTP_400_BAD_REQUEST)

        level = request.data.get('level')
        if not level:
            logger.error("Missing level")
            return Response({"error": "Level is required"},
                           status=status.HTTP_400_BAD_REQUEST)

        # Log the file details
        file_obj = request.FILES['transcript_file']
        logger.info(f"File received: {file_obj.name}, size: {file_obj.size} bytes, type: {file_obj.content_type}")

        try:
            # Look for existing transcript
            existing = InterviewTranscript.objects.filter(candidate_id=candidate_id, level=level).first()
            if existing:
                logger.info(f"Found existing transcript id={existing.id}, path={existing.transcript_file.path}")
                try:
                    if existing.transcript_file and os.path.exists(existing.transcript_file.path):
                        os.remove(existing.transcript_file.path)
                        logger.info("Deleted existing file")
                    existing.delete()
                    logger.info("Deleted existing record")
                except Exception as e:
                    logger.error(f"Error removing existing transcript: {str(e)}")
                    logger.error(traceback.format_exc())
                    # Continue anyway

            # Create new transcript
            logger.info("Creating new transcript")
            transcript = InterviewTranscript(
                candidate_id=candidate_id,
                level=level,
                transcript_file=file_obj
            )
            transcript.save()
            logger.info(f"Successfully created transcript id={transcript.id}, path={transcript.transcript_file.path}")

            # Return serialized response
            serializer = self.get_serializer(transcript)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating transcript: {str(e)}")
            logger.error(traceback.format_exc())
            return Response({"error": f"Error creating transcript: {str(e)}"},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create(self, request, *args, **kwargs):
        """Handle transcript uploads, replacing any existing transcript for the same level"""
        logger.info("Transcript upload request received")
        logger.info(f"Request data keys: {request.data.keys()}")
        logger.info(f"Request FILES keys: {request.FILES.keys() if hasattr(request, 'FILES') else 'No FILES'}")

        # Debug full request
        logger.debug(f"Content type: {request.content_type}")
        logger.debug(f"Method: {request.method}")
        logger.debug(f"Headers: {request.headers}")

        candidate_id = request.data.get('candidate')
        level = request.data.get('level')

        logger.info(f"Parsed from request: candidate_id={candidate_id}, level={level}")

        if not candidate_id or not level:
            error_msg = "Candidate ID and level are required"
            logger.error(error_msg)
            return Response({"error": error_msg},
                          status=status.HTTP_400_BAD_REQUEST)

        # Make sure the transcript_file is in the request
        if 'transcript_file' not in request.FILES:
            error_msg = "No transcript_file in request.FILES"
            logger.error(error_msg)
            return Response({"error": error_msg},
                          status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['transcript_file']
        logger.info(f"File received: name={file.name}, size={file.size}, content_type={file.content_type}")

        # Check if a transcript already exists for this candidate at this level
        try:
            existing = InterviewTranscript.objects.filter(candidate_id=candidate_id, level=level).first()
            if existing:
                logger.info(f"Found existing transcript: id={existing.id}")
                # If there's an existing file, delete it
                if existing.transcript_file:
                    file_path = existing.transcript_file.path
                    logger.info(f"Deleting existing file: {file_path}")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        logger.info("Existing file deleted successfully")
                    else:
                        logger.warning(f"Existing file not found at path: {file_path}")
                # Delete the existing record
                existing.delete()
                logger.info("Existing transcript record deleted")
        except Exception as e:
            logger.exception(f"Error handling existing transcript: {str(e)}")
            return Response({"error": f"Error processing existing transcript: {str(e)}"},
                          status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Create the serializer with data
        try:
            logger.info("Creating serializer with request data")
            serializer = self.get_serializer(data=request.data)

            if serializer.is_valid():
                logger.info("Serializer is valid, saving transcript")
                self.perform_create(serializer)
                headers = self.get_success_headers(serializer.data)
                return Response(
                    serializer.data,
                    status=status.HTTP_201_CREATED,
                    headers=headers
                )
            else:
                logger.error(f"Serializer validation failed: {serializer.errors}")
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            logger.exception(f"Exception during transcript creation: {str(e)}")
            return Response(
                {"error": f"Error creating transcript: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TranscriptEvaluationViewSet(viewsets.ModelViewSet):
    """
    API endpoints for transcript evaluations.
    """
    queryset = TranscriptEvaluation.objects.all()
    serializer_class = TranscriptEvaluationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter evaluations by transcript if transcript_id is provided"""
        queryset = TranscriptEvaluation.objects.all()
        transcript_id = self.request.query_params.get('transcript_id', None)
        if transcript_id:
            queryset = queryset.filter(transcript_id=transcript_id)
        return queryset

@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAuthenticated])
def evaluate_transcript(request):
    """
    Evaluates an interview transcript using the Ollama API.
    Expects transcript_id in the request.
    """
    transcript_id = request.query_params.get('transcript_id') or request.data.get('transcript_id')

    if not transcript_id:
        return Response({"error": "Transcript ID is required"},
                      status=status.HTTP_400_BAD_REQUEST)

    try:
        transcript = InterviewTranscript.objects.get(id=transcript_id)
    except InterviewTranscript.DoesNotExist:
        return Response({"error": "Transcript not found"},
                      status=status.HTTP_404_NOT_FOUND)

    logger.info(f"Evaluating transcript ID {transcript_id} for candidate {transcript.candidate.name}")

    # Extract text from the PDF
    transcript_text = ""
    try:
        with open(transcript.transcript_file.path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                transcript_text += page.extract_text()

        logger.info(f"Successfully extracted text from PDF, length: {len(transcript_text)} characters")
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {str(e)}")
        logger.error(traceback.format_exc())
        return Response({"error": f"Failed to extract text from PDF: {str(e)}"},
                      status=status.HTTP_400_BAD_REQUEST)

    if not transcript_text:
        logger.error("Could not extract text from the transcript PDF")
        return Response({"error": "Could not extract text from the transcript PDF"},
                      status=status.HTTP_400_BAD_REQUEST)

    # Handle large transcripts by summarizing/chunking
    MAX_TRANSCRIPT_LENGTH = 3000  # Characters, not tokens, but a reasonable limit
    if len(transcript_text) > MAX_TRANSCRIPT_LENGTH:
        logger.info(f"Transcript is too large ({len(transcript_text)} chars), truncating to {MAX_TRANSCRIPT_LENGTH} chars")
        # Keep the beginning and end of the transcript
        beginning = transcript_text[:MAX_TRANSCRIPT_LENGTH // 2]
        ending = transcript_text[-(MAX_TRANSCRIPT_LENGTH // 2):]
        transcript_text = beginning + "\n\n[... transcript truncated for length ...]\n\n" + ending

    # Prepare prompt for the Ollama model
    prompt = f"""
    Task: Evaluate a candidate's interview transcript for technical proficiency.

    INTERVIEW TRANSCRIPT FOR {transcript.candidate.name} (LEVEL {transcript.level}):
    {transcript_text}

    Please analyze this technical interview transcript and identify:
    1. The candidate's technical strengths
    2. Areas where the candidate could improve
    3. Any instances where the candidate's answers seem memorized or plagiarized from textbooks/online resources

    Provide a performance score from 0 to 10 (where 10 is excellent).

    Format your response as a JSON object with these fields:
    1. 'score': A number from 0 to 10
    2. 'technical_strengths': A text description of the candidate's technical strengths
    3. 'improvement_areas': A text description of areas where the candidate could improve
    4. 'plagiarism_concerns': Any instances where answers seem memorized or plagiarized
    5. 'detailed_report': A comprehensive evaluation (250-500 words)

    Response (JSON):
    """

    logger.info(f"Sending prompt to Ollama API, length: {len(prompt)} characters")

    try:
        # Call Ollama API with increased timeout
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "cogito:3b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": 1024,  # Limit output size
                    "temperature": 0.7,   # Add some creativity but not too much
                    "top_k": 40,          # Reasonable diversification
                    "top_p": 0.9          # Reasonable diversification
                }
            },
            timeout=120  # 2-minute timeout
        )

        if response.status_code != 200:
            logger.error(f"Ollama API returned non-200 status code: {response.status_code}")
            logger.error(f"Response content: {response.text}")
            return Response({"error": f"Ollama API error: HTTP {response.status_code}"},
                          status=status.HTTP_502_BAD_GATEWAY)

        logger.info("Successfully received response from Ollama API")

        response_data = response.json()
        response_text = response_data.get('response', '')

        logger.info(f"Raw response from Ollama API: {response_text[:100]}...")

        # Extract JSON from response - handle various formats
        json_text = ""
        try:
            # Try to find JSON within markdown code blocks first
            json_match = re.search(r'```(?:json)?\s*({.+?})\s*```', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
            else:
                # Try to find any JSON-like structure
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1

                if json_start >= 0 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                else:
                    # Last resort - try to use the whole text
                    json_text = response_text

            # Sanitize the JSON text to handle control characters
            # Replace common problematic control characters
            json_text = re.sub(r'[\x00-\x1F\x7F]', ' ', json_text)

            logger.info(f"Extracted JSON: {json_text[:100]}...")

            try:
                # First try normal JSON parsing
                result = json.loads(json_text)
            except json.JSONDecodeError:
                # If that fails, try a more aggressive sanitization
                logger.info("Initial JSON parsing failed, trying with more aggressive sanitization")
                # Create a pattern to match property names and their values
                pattern = r'"([^"]+)"\s*:\s*"([^"]*)"|"([^"]+)"\s*:\s*(\d+)|"([^"]+)"\s*:\s*(\{[^}]*\})|"([^"]+)"\s*:\s*(\[[^\]]*\])'
                matches = re.findall(pattern, json_text)

                # Reconstruct a clean JSON object
                result = {}
                for match in matches:
                    # Process each matched group to find key-value pairs
                    for i in range(0, len(match), 2):
                        if i+1 < len(match) and match[i]:
                            key = match[i]
                            value = match[i+1]
                            if value.isdigit():
                                result[key] = int(value)
                            else:
                                result[key] = value

            # Ensure we have the expected fields
            if 'score' not in result:
                result['score'] = 5  # Default to middle score
            if 'technical_strengths' not in result:
                result['technical_strengths'] = "Analysis of technical strengths not available."
            if 'improvement_areas' not in result:
                result['improvement_areas'] = "Analysis of improvement areas not available."
            if 'plagiarism_concerns' not in result:
                result['plagiarism_concerns'] = "No plagiarism analysis available."
            if 'detailed_report' not in result:
                result['detailed_report'] = "Detailed evaluation not available."

            # Ensure score is a number between 0 and 10
            try:
                result['score'] = int(float(result['score']))
                if result['score'] < 0:
                    result['score'] = 0
                elif result['score'] > 10:
                    result['score'] = 10
            except:
                result['score'] = 5  # Default to middle score

            logger.info(f"Successfully parsed JSON response. Score: {result['score']}")

            # Create a text report
            report_filename = f"evaluation_{uuid.uuid4()}.txt"
            report_path = os.path.join(settings.MEDIA_ROOT, 'evaluation_reports',
                                     f'candidate_{transcript.candidate.id}',
                                     f'level_{transcript.level}')
            os.makedirs(report_path, exist_ok=True)

            full_report_path = os.path.join(report_path, report_filename)

            # Write the report to a file
            with open(full_report_path, 'w') as f:
                f.write(f"EVALUATION REPORT FOR {transcript.candidate.name}\n")
                f.write(f"INTERVIEW LEVEL: {transcript.get_level_display()}\n")
                f.write(f"DATE: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n")
                f.write(f"SCORE: {result['score']}/10\n\n")
                f.write("TECHNICAL STRENGTHS:\n")
                f.write(result['technical_strengths'])
                f.write("\n\nIMPROVEMENT AREAS:\n")
                f.write(result['improvement_areas'])
                f.write("\n\nPLAGIARISM CONCERNS:\n")
                f.write(result['plagiarism_concerns'])
                f.write("\n\nDETAILED REPORT:\n")
                f.write(result['detailed_report'])

            logger.info(f"Successfully created evaluation report at {full_report_path}")

            # Save relative path for Django FileField
            relative_path = os.path.join('evaluation_reports',
                                        f'candidate_{transcript.candidate.id}',
                                        f'level_{transcript.level}',
                                        report_filename)

            # Create or update evaluation object
            evaluation, created = TranscriptEvaluation.objects.update_or_create(
                transcript=transcript,
                defaults={
                    'score': result['score'],
                    'technical_strengths': result.get('technical_strengths', ''),
                    'improvement_areas': result['improvement_areas'],
                    'plagiarism_concerns': result['plagiarism_concerns'],
                    'detailed_report': result['detailed_report'],
                    'report_file': relative_path
                }
            )

            # Update transcript to indicate it has been evaluated
            transcript.has_evaluation = True
            transcript.save()

            logger.info(f"Successfully saved evaluation to database, ID: {evaluation.id}")

            # Return the evaluation result
            serializer = TranscriptEvaluationSerializer(evaluation)
            return Response(serializer.data)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON from model response: {str(e)}")
            logger.error(f"JSON text attempted to parse: {json_text}")

            # Try a fallback approach - generate a default evaluation
            default_evaluation = {
                'score': 5,
                'technical_strengths': "Unable to analyze technical strengths due to AI processing error.",
                'improvement_areas': "Unable to analyze improvement areas due to AI processing error.",
                'plagiarism_concerns': "Unable to analyze plagiarism due to AI processing error.",
                'detailed_report': f"The AI model was unable to provide a structured evaluation. The raw response was: {response_text[:500]}..."
            }

            # Create a text report with the default evaluation
            report_filename = f"evaluation_{uuid.uuid4()}.txt"
            report_path = os.path.join(settings.MEDIA_ROOT, 'evaluation_reports',
                                     f'candidate_{transcript.candidate.id}',
                                     f'level_{transcript.level}')
            os.makedirs(report_path, exist_ok=True)

            full_report_path = os.path.join(report_path, report_filename)

            # Write the report to a file
            with open(full_report_path, 'w') as f:
                f.write(f"EVALUATION REPORT FOR {transcript.candidate.name}\n")
                f.write(f"INTERVIEW LEVEL: {transcript.get_level_display()}\n")
                f.write(f"DATE: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n")
                f.write(f"NOTE: This is an automatically generated fallback report due to AI processing error.\n\n")
                f.write(f"SCORE: {default_evaluation['score']}/10\n\n")
                f.write("TECHNICAL STRENGTHS:\n")
                f.write(default_evaluation['technical_strengths'])
                f.write("\n\nIMPROVEMENT AREAS:\n")
                f.write(default_evaluation['improvement_areas'])
                f.write("\n\nPLAGIARISM CONCERNS:\n")
                f.write(default_evaluation['plagiarism_concerns'])
                f.write("\n\nDETAILED REPORT:\n")
                f.write(default_evaluation['detailed_report'])
                f.write("\n\nRAW AI RESPONSE:\n")
                f.write(response_text[:1000])

            # Save relative path for Django FileField
            relative_path = os.path.join('evaluation_reports',
                                        f'candidate_{transcript.candidate.id}',
                                        f'level_{transcript.level}',
                                        report_filename)

            # Create or update evaluation object
            evaluation, created = TranscriptEvaluation.objects.update_or_create(
                transcript=transcript,
                defaults={
                    'score': default_evaluation['score'],
                    'technical_strengths': default_evaluation.get('technical_strengths', ''),
                    'improvement_areas': default_evaluation['improvement_areas'],
                    'plagiarism_concerns': default_evaluation['plagiarism_concerns'],
                    'detailed_report': default_evaluation['detailed_report'],
                    'report_file': relative_path
                }
            )

            # Update transcript to indicate it has been evaluated
            transcript.has_evaluation = True
            transcript.save()

            logger.info(f"Successfully saved fallback evaluation to database, ID: {evaluation.id}")

            # Return the evaluation result
            serializer = TranscriptEvaluationSerializer(evaluation)
            return Response(serializer.data)
    except requests.exceptions.RequestException as e:
        logger.error(f"API request error: {str(e)}")
        logger.error(traceback.format_exc())
        return Response({"error": f"API request error: {str(e)}"},
                      status=status.HTTP_502_BAD_GATEWAY)
    except Exception as e:
        logger.error(f"Unexpected error in evaluate_transcript: {str(e)}")
        logger.error(traceback.format_exc())
        return Response({"error": f"Unexpected error: {str(e)}"},
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def download_evaluation_report(request):
    """
    Download the evaluation report file.
    This view bypasses DRF content negotiation by directly using Django's FileResponse.
    """
    from django.http import FileResponse

    evaluation_id = request.query_params.get('evaluation_id')

    logger.info(f"Download request for evaluation ID: {evaluation_id}")
    logger.info(f"Request headers: {dict(request.headers)}")

    if not evaluation_id:
        logger.error("No evaluation_id provided in request")
        return Response({"error": "Evaluation ID is required"},
                      status=status.HTTP_400_BAD_REQUEST)

    # Check for authentication
    if not request.user.is_authenticated:
        logger.error("Unauthenticated user attempted to download a report")
        return Response({"error": "Authentication required"},
                       status=status.HTTP_401_UNAUTHORIZED)

    try:
        evaluation = TranscriptEvaluation.objects.get(id=evaluation_id)
        logger.info(f"Found evaluation: {evaluation}")
    except TranscriptEvaluation.DoesNotExist:
        logger.error(f"Evaluation with ID {evaluation_id} not found")
        return Response({"error": "Evaluation not found"},
                      status=status.HTTP_404_NOT_FOUND)

    # Even if no file is available, we can generate one from the evaluation data
    if not evaluation.report_file or not os.path.exists(evaluation.report_file.path if evaluation.report_file else ''):
        logger.info(f"Report file doesn't exist or is missing. Generating a new one.")

        # Create a report file from the evaluation data
        report_filename = f"evaluation_{uuid.uuid4()}.txt"
        report_path = os.path.join(settings.MEDIA_ROOT, 'evaluation_reports',
                                 f'candidate_{evaluation.transcript.candidate.id}',
                                 f'level_{evaluation.transcript.level}')
        os.makedirs(report_path, exist_ok=True)

        full_report_path = os.path.join(report_path, report_filename)

        # Write the report to a file
        with open(full_report_path, 'w') as f:
            f.write(f"EVALUATION REPORT FOR {evaluation.transcript.candidate.name}\n")
            f.write(f"INTERVIEW LEVEL: {evaluation.transcript.get_level_display()}\n")
            f.write(f"DATE: {evaluation.updated_at.strftime('%Y-%m-%d %H:%M')}\n\n")
            f.write(f"SCORE: {evaluation.score}/10\n\n")
            f.write("TECHNICAL STRENGTHS:\n")
            f.write(evaluation.technical_strengths or "Not evaluated")
            f.write("\n\nIMPROVEMENT AREAS:\n")
            f.write(evaluation.improvement_areas or "Not evaluated")
            f.write("\n\nPLAGIARISM CONCERNS:\n")
            f.write(evaluation.plagiarism_concerns or "Not evaluated")
            f.write("\n\nDETAILED REPORT:\n")
            f.write(evaluation.detailed_report or "Not evaluated")

        # Save the new file path to the evaluation
        relative_path = os.path.join('evaluation_reports',
                                    f'candidate_{evaluation.transcript.candidate.id}',
                                    f'level_{evaluation.transcript.level}',
                                    report_filename)
        evaluation.report_file = relative_path
        evaluation.save()

        logger.info(f"Created new report file at {full_report_path}")

    try:
        file_path = evaluation.report_file.path
        logger.info(f"Serving report file from: {file_path}")

        if os.path.exists(file_path):
            # Use Django's FileResponse which handles files efficiently
            # and sets appropriate headers
            safe_filename = f"evaluation_report_{evaluation_id}.txt"

            # Open the file
            file_obj = open(file_path, 'rb')

            # Create a FileResponse
            response = FileResponse(
                file_obj,
                as_attachment=True,
                filename=safe_filename
            )

            # Add additional headers for better browser compatibility
            response['Content-Type'] = 'application/octet-stream'
            response['Content-Length'] = os.path.getsize(file_path)
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition, Content-Length'

            logger.info(f"Successfully serving report file with size: {os.path.getsize(file_path)} bytes")
            logger.info(f"Response headers: {dict(response.headers)}")

            return response
        else:
            logger.error(f"Report file not found on disk: {file_path}")
            return Response({"error": "Report file not found on disk"},
                          status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Failed to download report: {str(e)}")
        logger.error(traceback.format_exc())
        return Response({"error": f"Failed to download report: {str(e)}"},
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'POST'])
def upload_transcript_direct(request):
    """
    A direct file upload view that bypasses DRF serializers for simplicity.
    This is a fallback approach to ensure the file upload works.
    """
    success = False
    message = ""

    if request.method == 'POST':
        candidate_id = request.POST.get('candidate')
        level = request.POST.get('level')
        transcript_file = request.FILES.get('transcript_file')

        if not candidate_id or not level or not transcript_file:
            message = "All fields are required: candidate ID, level, and PDF file."
        else:
            try:
                # Check if candidate exists
                try:
                    candidate = Candidate.objects.get(id=candidate_id)
                except Candidate.DoesNotExist:
                    message = f"Candidate with ID {candidate_id} not found."
                    return render(request, 'upload_transcript.html', {
                        'success': False,
                        'message': message
                    })

                # Check if file is a PDF
                if not transcript_file.name.lower().endswith('.pdf'):
                    message = "File must be a PDF."
                    return render(request, 'upload_transcript.html', {
                        'success': False,
                        'message': message
                    })

                # Check if a transcript already exists for this candidate at this level
                existing = InterviewTranscript.objects.filter(candidate=candidate, level=level).first()
                if existing:
                    # Delete the existing file
                    if existing.transcript_file:
                        if os.path.exists(existing.transcript_file.path):
                            os.remove(existing.transcript_file.path)
                    # Delete the existing record
                    existing.delete()

                # Create the new transcript
                transcript = InterviewTranscript.objects.create(
                    candidate=candidate,
                    level=level,
                    transcript_file=transcript_file
                )

                success = True
                message = f"Transcript for {candidate.name} (Level {level}) uploaded successfully!"

            except Exception as e:
                message = f"Error uploading transcript: {str(e)}"

    return render(request, 'upload_transcript.html', {
        'success': success,
        'message': message
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def debug_transcripts(request):
    """
    Diagnostic view to check all transcripts in the database and their files on disk
    """
    try:
        candidate_id = request.query_params.get('candidate_id')
        logger.info(f"Debug transcripts called with candidate_id: {candidate_id}")

        # Get transcripts
        if candidate_id:
            try:
                candidate_id = int(candidate_id)
                transcripts = InterviewTranscript.objects.filter(candidate_id=candidate_id)
                logger.info(f"Found {transcripts.count()} transcripts for candidate {candidate_id}")
            except ValueError:
                logger.error(f"Invalid candidate_id format: {candidate_id}")
                return Response({"error": f"Invalid candidate_id format: {candidate_id}"},
                               status=status.HTTP_400_BAD_REQUEST)
        else:
            transcripts = InterviewTranscript.objects.all()
            logger.info(f"Found {transcripts.count()} total transcripts")

        result = []
        for t in transcripts:
            # Check if file exists
            file_exists = False
            file_path = ""
            file_size = 0

            try:
                if t.transcript_file:
                    file_path = str(t.transcript_file.path)  # Convert Path to string
                    file_exists = os.path.exists(file_path)
                    if file_exists:
                        file_size = os.path.getsize(file_path)
            except Exception as e:
                logger.error(f"Error checking file for transcript {t.id}: {str(e)}")

            result.append({
                "id": t.id,
                "candidate_id": t.candidate_id,
                "candidate_name": t.candidate.name,
                "level": t.level,
                "uploaded_at": t.uploaded_at.isoformat() if t.uploaded_at else None,
                "file_path": file_path,  # Now contains a string
                "file_exists": file_exists,
                "file_size": file_size,
                "db_filename": str(t.transcript_file.name) if t.transcript_file else None,  # Convert to string
            })

        # Check media directory
        media_dir = os.path.join(settings.MEDIA_ROOT, 'interview_transcripts')
        files_on_disk = []

        if os.path.exists(media_dir):
            for filename in os.listdir(media_dir):
                try:
                    file_path = os.path.join(media_dir, filename)
                    if os.path.isfile(file_path):
                        files_on_disk.append({
                            "filename": filename,
                            "path": str(file_path),  # Convert Path to string
                            "size": os.path.getsize(file_path),
                            "created": datetime.fromtimestamp(os.path.getctime(file_path)).isoformat(),
                        })
                except Exception as e:
                    logger.error(f"Error getting info for file {filename}: {str(e)}")

        # Database query info
        db_info = []
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT * FROM candidates_interviewtranscript")
                columns = [col[0] for col in cursor.description]
                for row in cursor.fetchall():
                    # Convert any Path objects to strings
                    processed_row = []
                    for item in row:
                        if hasattr(item, '__fspath__'):  # Check if it's a path-like object
                            processed_row.append(str(item))
                        else:
                            processed_row.append(item)
                    db_info.append(dict(zip(columns, processed_row)))
        except Exception as e:
            logger.error(f"Error fetching raw database records: {str(e)}")
            db_info = [{"error": str(e)}]

        # Overall system info
        system_info = {
            "media_root": str(settings.MEDIA_ROOT),  # Convert Path to string
            "transcripts_dir": str(media_dir),  # Convert Path to string
            "dir_exists": os.path.exists(media_dir),
            "total_db_transcripts": transcripts.count(),
            "total_files_on_disk": len(files_on_disk),
        }

        return Response({
            "system_info": system_info,
            "transcripts": result,
            "files_on_disk": files_on_disk,
            "raw_db_records": db_info,
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in debug_transcripts: {str(e)}")
        logger.error(traceback.format_exc())
        return Response({
            "error": str(e),
            "traceback": traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def simple_test_view(request):
    """A very simple view that returns basic HTML to test if Django views are working."""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Test Page</title>
    </head>
    <body>
        <h1>This is a test page</h1>
        <p>If you can see this, Django views are working correctly.</p>
    </body>
    </html>
    """
    return HttpResponse(html)
