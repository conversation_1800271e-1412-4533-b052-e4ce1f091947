import React, { useState } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

const QAGeneratorModal = ({ isOpen, onClose, job, darkMode = false }) => {
  const [numQuestions, setNumQuestions] = useState(5);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [questions, setQuestions] = useState([]);

  const handleGenerateQuestions = async () => {
    setIsLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${API_URL}/api/jobs/generate-qa/`,
        {
          job_id: job.id,
          num_questions: numQuestions
        },
        {
          headers: {
            'Authorization': `Token ${token}`
          }
        }
      );
      
      setQuestions(prevQuestions => [...prevQuestions, ...(response.data.questions || [])]);
    } catch (err) {
      console.error('Error generating questions:', err);
      setError(err.response?.data?.detail || 'Failed to generate questions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteQuestion = (indexToDelete) => {
    setQuestions(prevQuestions => 
      prevQuestions.filter((_, index) => index !== indexToDelete)
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center overflow-y-auto">
      <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden m-4`}>
        <div className="p-6 flex flex-col h-full">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Generate Q&A for {job.title_display}</h2>
            <button
              onClick={onClose}
              className={`${darkMode ? 'text-gray-300 hover:text-gray-100' : 'text-gray-500 hover:text-gray-700'}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-6">
            <label htmlFor="numQuestions" className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
              Number of questions to generate
            </label>
            <div className="flex items-center space-x-3">
              <input
                id="numQuestions"
                type="number"
                min="1"
                max="20"
                value={numQuestions}
                onChange={(e) => setNumQuestions(Math.min(Math.max(1, parseInt(e.target.value) || 1), 20))}
                className={`w-24 p-2 border ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-800'} rounded`}
              />
              <button
                onClick={handleGenerateQuestions}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Generating...' : 'Generate Questions'}
              </button>
            </div>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
              {job.description_document_filename 
                ? `Questions will be based on the JD document: ${job.description_document_filename}` 
                : 'No JD document available. Questions will be based on the job title.'}
            </p>
          </div>

          {error && (
            <div className={`${darkMode ? 'bg-red-900 text-red-200' : 'bg-red-50 text-red-700'} p-3 rounded mb-4`}>
              {error}
            </div>
          )}

          <div className="flex-1 overflow-auto">
            {questions.length > 0 && (
              <div className="flex justify-between items-center mb-4">
                <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-800'}`}>Generated Questions & Answers:</h3>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {questions.length} question{questions.length !== 1 ? 's' : ''} total
                </div>
              </div>
            )}
            
            {isLoading && (
              <div className={`mb-6 p-4 border ${darkMode ? 'border-blue-700 bg-blue-900 text-blue-200' : 'border-blue-200 bg-blue-50 text-blue-700'} rounded-lg flex items-center`}>
                <div className={`animate-spin rounded-full h-6 w-6 border-b-2 ${darkMode ? 'border-blue-400' : 'border-blue-700'} mr-3`}></div>
                <p>Generating more questions...</p>
              </div>
            )}
            
            {questions.length > 0 && (
              <div className="space-y-6">
                {questions.map((qa, index) => (
                  <div key={index} className={`border ${darkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-50'} rounded-lg p-4 relative`}>
                    <button 
                      onClick={() => handleDeleteQuestion(index)}
                      className={`absolute top-2 right-2 ${darkMode ? 'text-red-400 hover:text-red-300' : 'text-red-500 hover:text-red-700'}`}
                      title="Delete question"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                    <div className="mb-3">
                      <h4 className={`font-semibold ${darkMode ? 'text-blue-300' : 'text-blue-700'}`}>Q{index + 1}: {qa.question}</h4>
                    </div>
                    <div>
                      <h5 className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Expected Answer:</h5>
                      <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{qa.answer}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {!isLoading && questions.length === 0 && (
              <div className={`text-center py-10 ${darkMode ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg`}>
                <p className={`${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>No questions generated yet. Click "Generate Questions" to create interview questions.</p>
              </div>
            )}
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className={`px-4 py-2 ${darkMode ? 'bg-gray-700 text-gray-200 hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded`}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QAGeneratorModal;
