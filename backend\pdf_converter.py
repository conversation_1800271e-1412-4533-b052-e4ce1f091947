"""
Simple script to convert a text file to PDF
"""
import sys
from fpdf import FPD<PERSON>

def convert_txt_to_pdf(txt_file, pdf_file):
    try:
        # Read the text file
        with open(txt_file, 'r', encoding='utf-8') as file:
            text = file.read()
        
        # Create PDF
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        
        # Add text to PDF (handle line breaks)
        for line in text.split('\n'):
            pdf.multi_cell(0, 10, line)
        
        # Save PDF
        pdf.output(pdf_file)
        print(f"Successfully converted {txt_file} to {pdf_file}")
        return True
    except Exception as e:
        print(f"Error converting file: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python pdf_converter.py input.txt output.pdf")
        sys.exit(1)
    
    txt_file = sys.argv[1]
    pdf_file = sys.argv[2]
    
    if not convert_txt_to_pdf(txt_file, pdf_file):
        sys.exit(1)
