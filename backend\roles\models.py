from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class Role(models.Model):
    """
    Dynamic role model to replace hardcoded role choices
    """
    value = models.CharField(max_length=50, unique=True, help_text="System identifier (snake_case)")
    label = models.CharField(max_length=100, help_text="Human-readable display name")
    is_active = models.BooleanField(default=True, help_text="Whether this role is available for selection")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_roles')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['label']

    def __str__(self):
        return self.label

    def save(self, *args, **kwargs):
        # Ensure value is always snake_case
        if self.value:
            self.value = self.value.lower().replace(' ', '_').replace('-', '_')
        super().save(*args, **kwargs)
