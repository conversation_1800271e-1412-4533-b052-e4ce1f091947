"""
URL configuration for talent_hero project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from rest_framework.authtoken.views import obtain_auth_token
from django.http import JsonResponse
from django.conf import settings
from django.conf.urls.static import static
from candidates.views import rank_candidates  # Import the correct view function

def api_root(request):
    return JsonResponse({
        'message': 'Welcome to Talent Hero API',
        'version': '1.0.0',
        'endpoints': {
            'admin': '/admin/',
            'token_auth': '/api/token/',
            'auth': {
                'login': '/api/auth/login/',
                'logout': '/api/auth/logout/',
                'register': '/api/auth/register/',
                'create_user': '/api/auth/admin/create-user/',
                'users': '/api/auth/users/',
            },
            'roles': '/api/roles/',
            'jobs': '/api/jobs/',
            'candidates': '/api/candidates/',
        }
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/token/', obtain_auth_token, name='api_token_auth'),
    path('api/auth/', include('authentication.urls')),
    path('api/roles/', include('roles.urls')),
    path('api/jobs/', include('jobs.urls')),
    path('api/candidates/', include('candidates.urls')),
    # Add a direct path to rank_candidates to bypass any routing issues
    path('api/rank-candidates/', rank_candidates, name='rank-candidates-direct'),
    path('', api_root, name='api-root'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
