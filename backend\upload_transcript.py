"""
Simple file upload script for interview transcripts.
This bypasses the Django REST framework and uses Django's direct file handling.
"""
import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talent_hero.settings')
django.setup()

from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from candidates.models import Candidate, InterviewTranscript
import json

def upload_transcript(candidate_id, level, file_path):
    """
    Upload a transcript file for a candidate
    
    Args:
        candidate_id: ID of the candidate
        level: Interview level (L1, L2, L3, L4)
        file_path: Path to the PDF file to upload
    
    Returns:
        dict: Result of the upload operation
    """
    try:
        # Check if candidate exists
        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except Candidate.DoesNotExist:
            return {"success": False, "error": f"Candidate with ID {candidate_id} not found"}
        
        # Validate level
        if level not in ['L1', 'L2', 'L3', 'L4']:
            return {"success": False, "error": f"Invalid level: {level}. Must be one of: L1, L2, L3, L4"}
        
        # Check if file exists
        if not os.path.exists(file_path):
            return {"success": False, "error": f"File not found: {file_path}"}
        
        # Check if it's a PDF
        if not file_path.lower().endswith('.pdf'):
            return {"success": False, "error": "File must be a PDF"}
        
        # Check if a transcript already exists for this candidate at this level
        existing = InterviewTranscript.objects.filter(candidate=candidate, level=level).first()
        if existing:
            # Delete the existing file
            if existing.transcript_file:
                if os.path.exists(existing.transcript_file.path):
                    os.remove(existing.transcript_file.path)
            # Delete the existing record
            existing.delete()
        
        # Create the destination path
        filename = os.path.basename(file_path)
        storage_path = f'interview_transcripts/candidate_{candidate.id}/level_{level}/{filename}'
        
        # Copy the file to the media directory
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # Save the file using Django's storage
        saved_path = default_storage.save(storage_path, ContentFile(file_content))
        
        # Create the transcript record
        transcript = InterviewTranscript.objects.create(
            candidate=candidate,
            level=level,
            transcript_file=saved_path
        )
        
        return {
            "success": True, 
            "id": transcript.id,
            "message": f"Transcript for {candidate.name} (Level {level}) uploaded successfully"
        }
        
    except Exception as e:
        return {"success": False, "error": str(e)}

def main():
    """Command-line interface for the script"""
    if len(sys.argv) != 4:
        print("Usage: python upload_transcript.py <candidate_id> <level> <file_path>")
        sys.exit(1)
    
    candidate_id = sys.argv[1]
    level = sys.argv[2]
    file_path = sys.argv[3]
    
    result = upload_transcript(candidate_id, level, file_path)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
